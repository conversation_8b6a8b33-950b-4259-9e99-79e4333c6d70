package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: LoginMOTest
 * @author: Justin
 * @Date: 2024-05-18
 **/
public class LoginMOTest {


    @Test
    public void LoginMOTest1(){
        String hexString = "4f4459344f544d314d4459774d4449304e6a4533";
        LoginMO loginMO = new LoginMO(Hex.fromHexString(hexString));
        ObjectUtils.prettyPrint(loginMO);
    }
}
