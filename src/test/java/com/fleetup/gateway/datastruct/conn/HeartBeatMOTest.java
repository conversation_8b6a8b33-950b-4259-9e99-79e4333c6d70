package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: HeartBeatMOTest
 * @author: Justin
 * @Date: 2024-05-18
 **/
public class HeartBeatMOTest {

    @Test
    public void HeartBeatMOTest1(){
        String hexString = "7e000200004f07788efe1d002d727e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        ObjectUtils.prettyPrint(baseProtocol);
    }
}
