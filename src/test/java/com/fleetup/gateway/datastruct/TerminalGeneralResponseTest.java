package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: TerminalGeneralResponseTest
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class TerminalGeneralResponseTest {

    @Test
    public void PlatformGeneralResponseTest1(){
        String hexString = "7e000100054f07788efe1d003f3d000100015b7e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        TerminalGeneralResponse terminalGeneralResponse = new TerminalGeneralResponse(baseProtocol.getPayload().toBytes());
        ObjectUtils.prettyPrint(terminalGeneralResponse);
    }

}
