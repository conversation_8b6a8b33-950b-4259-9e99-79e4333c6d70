package com.fleetup.gateway.datastruct.report;


import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: LocationReportTest
 * @author: Justin
 * @Date: 2024-05-19
 **/
public class LocationReportTest {

    @Test
    public void LocationReportTest1(){
        String hexString = "7e020000264f07788efe1d001000000000800c000201629e4a06c0c8d8001d000000002405171407382a020000300112310108b57e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        LocationReport locationReport = new LocationReport(baseProtocol.getPayload().toBytes());
        ObjectUtils.prettyPrint(locationReport);
    }

}
