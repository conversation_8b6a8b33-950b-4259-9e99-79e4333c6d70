package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: BaseProtocolTest
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class BaseProtocolTest {

    @Test
    public void test(){
        String hexString = "7e010200144f07788efe1d002c4f4459344f544d314d4459774d4449304e6a4533627e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        ObjectUtils.prettyPrint(baseProtocol);
    }



}
