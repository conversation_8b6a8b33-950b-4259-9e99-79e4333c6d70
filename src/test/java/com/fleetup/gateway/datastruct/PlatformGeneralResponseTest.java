package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import org.junit.Test;

/**
 * @className: PlatformGeneralResponseTest
 * @author: Justin
 * @Date: 2024-05-18
 **/
public class PlatformGeneralResponseTest {

    @Test
    public void PlatformGeneralResponseTest1(){
        String hexString = "7e800100054f07788efe1d00010059000200837e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        PlatformGeneralResponse generalResponse = new PlatformGeneralResponse(baseProtocol.getPayload().toBytes());
        ObjectUtils.prettyPrint(generalResponse);
    }

}
