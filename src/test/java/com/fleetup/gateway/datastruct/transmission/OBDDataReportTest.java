package com.fleetup.gateway.datastruct.transmission;


import com.alibaba.fastjson.JSON;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.event.GpsEvent;
import com.fleetup.gateway.server.handler.UplinkDataHandler;
import com.fleetup.gateway.util.Hex;
import org.junit.Test;

/**
 * @className: OBDDataReportTest
 * @author: Justin
 * @Date: 2024-05-30
 **/
public class OBDDataReportTest {

    @Test
    public void OBDDataReportTest1(){
        String hexString = "7e090000654f07788ef8bf00e8f02405290125430002010f05280400035c14052b015d052c04000010c7052d0164052e01520530023750053502019005360204ca053802037f0539020166053d0203200544015d05460400001543054504000000210547010a000c000b02398f980744db208d7e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        UplinkData uplinkData = new UplinkData(baseProtocol.getPayload().toBytes());
        F0Data data = new F0Data(uplinkData.getPayload().toBytes());
        OBDDataReport obdDataReport = new OBDDataReport(data.getPayload().toBytes());
        UplinkDataHandler uplinkDataHandler = new UplinkDataHandler();
        GpsEvent gpsEvent = uplinkDataHandler.buildGpsEvent(data, obdDataReport);
        System.out.println(JSON.toJSONString(gpsEvent, true));
    }
}
