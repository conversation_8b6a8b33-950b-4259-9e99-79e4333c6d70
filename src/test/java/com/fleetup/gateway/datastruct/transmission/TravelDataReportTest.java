package com.fleetup.gateway.datastruct.transmission;


import com.alibaba.fastjson.JSON;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.event.TravelDataEvent;
import com.fleetup.gateway.server.handler.UplinkDataHandler;
import com.fleetup.gateway.util.Hex;
import org.junit.Test;

/**
 * @className: TravelDataReportTest
 * @author: Justin
 * @Date: 2024-05-29
 **/
public class TravelDataReportTest {

    @Test
    public void TravelDataReportTest1(){
//        String hexString = "7e090000154f07788ef8fe00adf0240527045220000204010000001a240527045219dd7e";
        String hexString = "7e090000344f07788ef8fe029ef0240527053120000204020000001a2405270452192405270531190236f874073ff4c0023a34b0074380180a000d0002017600d7f67e";
        UplinkDataHandler uplinkDataHandler = new UplinkDataHandler();
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        String devId = baseProtocol.getDeviceId().getValue();
        System.out.println("devId: " +  devId);
        UplinkData uplinkData = new UplinkData(baseProtocol.getPayload().toBytes());
        F0Data data = new F0Data(uplinkData.getPayload().toBytes());
        TravelDataReport travelDataReport = new TravelDataReport(data.getPayload().toBytes());
        System.out.println("startTime: " + travelDataReport.getStartTime().readValue());
        if (travelDataReport.getTravelProperty().getValue() == 2){
            System.out.println("endTime  : " + travelDataReport.getEndTime().readValue());
        }
        TravelDataEvent travelDataEvent = uplinkDataHandler.buildTripEvent(travelDataReport);
        System.out.println(JSON.toJSONString(travelDataEvent, true));
    }

}
