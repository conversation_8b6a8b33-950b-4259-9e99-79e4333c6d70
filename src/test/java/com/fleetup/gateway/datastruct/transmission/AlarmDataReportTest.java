package com.fleetup.gateway.datastruct.transmission;


import com.alibaba.fastjson.JSON;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.event.AlarmEvent;
import com.fleetup.gateway.server.handler.UplinkDataHandler;
import com.fleetup.gateway.util.Hex;
import org.junit.Test;

/**
 * @className: AlarmDataReport
 * @author: Justin
 * @Date: 2024-05-28
 **/
public class AlarmDataReportTest {

    @Test
    public void AlarmDataReportTest1(){
        String hexString = "7e0900001c4f07788ef8fe0248f0240527052408000203012503313036000c000b0239a42807433250bf7e";
//        String hexString = "7e090000194f07788ef8bf02bdf0240528011125000203013800000c000b0239f06407453000787e";
//        String hexString = "7e090000194f07788ef8bf02cdf0240528011248000203011a00000c000b0239f248074518185a7e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        UplinkData uplinkData = new UplinkData(baseProtocol.getPayload().toBytes());
        F0Data f0Data = new F0Data(uplinkData.getPayload().toBytes());
        AlarmDataReport alarmDataReport = new AlarmDataReport(f0Data.getPayload().toBytes());


        UplinkDataHandler uplinkDataHandler = new UplinkDataHandler();
        AlarmEvent alarmEvent = uplinkDataHandler.buildAlarmEvent(f0Data, alarmDataReport);
        System.out.println(JSON.toJSONString(alarmEvent, true));
    }

}
