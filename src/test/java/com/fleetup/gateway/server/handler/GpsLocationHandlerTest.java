package com.fleetup.gateway.server.handler;


import com.alibaba.fastjson.JSON;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.report.LocationReport;
import com.fleetup.gateway.event.AlarmEvent;
import com.fleetup.gateway.event.GpsEvent;
import com.fleetup.gateway.util.Hex;
import org.junit.Test;

/**
 * @className: GpsLocationHandlerTest
 * @author: Justin
 * @Date: 2024-05-20
 **/
public class GpsLocationHandlerTest {

    @Test
    public void GpsLocationHandler(){
        GpsLocationHandler gpsLocationHandler = new GpsLocationHandler();
//        String hexString = "7e020000264f07788efe1d001000000000800c000201629e4a06c0c8d8001d000000002405171407382a020000300112310108b57e";
        String hexString = "7e020000264f07788ef8fe020b00000000000c000b02386b5007425140003b0494012f2405270518112a020000300117310114627e";
        BaseProtocol baseProtocol = new BaseProtocol(Hex.fromHexString(hexString));
        LocationReport locationReport = new LocationReport(baseProtocol.getPayload().toBytes());
        GpsEvent gpsEvent = gpsLocationHandler.buildGpsEvent(locationReport);
        AlarmEvent alarmEvent = gpsLocationHandler.buildAlarmEvent(gpsEvent, locationReport.getAlarmFlag().getValue());
        System.out.println(JSON.toJSONString(locationReport, true));
//        System.out.println(JSON.toJSONString(alarmEvent, true));
    }

}
