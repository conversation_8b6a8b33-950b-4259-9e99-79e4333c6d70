package com.fi.lambda.handler;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.sql.BatchUpdateException;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.contrib.java.lang.system.EnvironmentVariables;

import com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage;
import com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages;
import com.amazonaws.services.lambda.runtime.events.KinesisEvent;
import com.amazonaws.util.IOUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fi.gateway.producer.event.Gps;
import com.fi.gateway.producer.event.Stat;
import com.fi.lambda.common.TestContext;
import com.fi.lambda.dao.DeviceDao;
import com.fi.lambda.dao.GpsDataDao;
import com.fi.lambda.model.OldDevBasicSetting;
import com.fi.lambda.model.GpsData;
import com.fleetup.rds.Env;

/**
 * 
 * @see sample event data
 * {@link https://docs.aws.amazon.com/ko_kr/lambda/latest/dg/eventsources.html}
 * */
//@Ignore
public class KinesisConsumerNewTest {
	
	@Rule
	public final EnvironmentVariables environmentVariables = new EnvironmentVariables();

	ObjectMapper mapper = new ObjectMapper();
	GatewayOBDConsumer gatewayConsumer;
	KinesisEvent event;
	
	@Before
	public void setUp() throws Exception {
		Env.init(TestContext.SAMPLE_ARN_LOCAL);
		
		environmentVariables.set("STRESS_SECRET_ARN", "arn:aws:secretsmanager:us-west-2:994093938344:secret:/stress/db/oracle/consumerobd-mTepaw");
		
		gatewayConsumer = new GatewayOBDConsumer();
		mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
		event = mapper.readValue(this.getClass().getResourceAsStream("/samples/kinesis_event.json"), new TypeReference<KinesisEvent>() {});
	}

	
	@Test
	public void testConvertUnixTimeStampToLong() {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").withZone(ZoneId.systemDefault());
		Instant fromUnixTimestamp = Instant.ofEpochSecond(1262347200);
		long output = Long.parseLong(formatter.format(fromUnixTimestamp));
		System.out.println("output: " + output);
	}
	
	@Test
	public void testHexIntComparision() {
		DeviceDao deviceDao = new DeviceDao();
		deviceDao.updateDevBasicSetting(1.123f, 2.25f, 3, 4, "nwtest03200", 0, 0, 0, 0,135.0f,20,0);
		
		// 
		DecimalFormat df = new DecimalFormat("#.####");
		System.out.println(df.format(312222222000.25555));
		//System.out.println(String.format("%.4f", 312.2));
		
		
		OldDevBasicSetting dbs = new OldDevBasicSetting();
		dbs.setDevId("123");
		
		System.out.println(dbs.getTotalEngineHour());
		
		
		if (8064 == 0x1F80) {
			System.out.println("same");	
		}else {
			System.out.println("different");
		}
		
		int value = 0x1F80;
		switch (value) {
		case 8064:
			System.out.println("same");
			break;
		default:
			System.out.println("different");
			
		
		}
		
		
		value = 8064;
		switch (value) {
		case  0x1F80:
			System.out.println("same");
			break;
		default:
			System.out.println("different");
		
		}
		
		
	}
	
	@Test
	public void testBatchUpdateException() {
		
		Stat stat = new Stat();
		stat.setAcconTime(20180903051051L);
		stat.setCurrentTripMileage(200);
		stat.setCurrentFuel(1000);
		stat.setDeviceTime(20180903054908L);
		
		Gps gps = new Gps();
		gps.setSpeed(250);
		gps.setDirection(1840);
		gps.setLatitude(87784439);
		gps.setLongitude(416811332);
		
		List<GpsData> tmpListForReTry = new ArrayList<>();
		tmpListForReTry.add(createGpsData("ETEST000931", stat, gps, 310, 0));
		tmpListForReTry.add(createGpsData("ETEST000932", stat, gps, 320, 0));
		tmpListForReTry.add(createGpsData("ETEST000933", stat, gps, 330, 0));
		tmpListForReTry.add(createGpsData("ETEST000934", stat, gps, 340, 0));
		tmpListForReTry.add(createGpsData("ETEST000935", stat, gps, 350, 0));
		tmpListForReTry.add(createGpsData("ETEST000936", stat, gps, 360, 0));
		tmpListForReTry.add(createGpsData("ETEST000937", stat, gps, 370, 0));
		tmpListForReTry.add(createGpsData("ETEST000938", stat, gps, 380, 0));
		tmpListForReTry.add(createGpsData("ETEST000939", stat, gps, 390, 0));
		tmpListForReTry.add(createGpsData("ETEST000940", stat, gps, 400, 0));
		
		try {
			int[] updateCounts = { -2, -2, -2, -2, -2 };
			throw new BatchUpdateException(updateCounts);
		} catch (BatchUpdateException buex) {
			System.out.println("[ERROR] " + buex.toString());
			buex.printStackTrace();
			
			if (buex.getUpdateCounts() != null) {
				int[] updateCounts = buex.getUpdateCounts();
				int totalCount = tmpListForReTry.size();
				int successCount = updateCounts.length;
				int failCount = totalCount - successCount;
				
				System.out.println("[RETRY] Start. Total: " + totalCount + ", Success: " + successCount + ", Fail: " + failCount + " (MAX_RETRY_COUNT: " + GpsDataDao.MAX_RETRY_COUNT_GPS_DATA + ")");
				for(int i = successCount; i < totalCount; i++) {
					
					int reInsertedCount = 0;
					int retryCount = 0;

					while (reInsertedCount < 1 && retryCount < GpsDataDao.MAX_RETRY_COUNT_GPS_DATA) {
						// reInsertedCount = dao.insertGpsData(tmpListForReTry.get(i));
						reInsertedCount = 1;
						retryCount++;
						failCount--;
					}
					System.out.println("[RETRY] Done.(" + (i + 1) + "/" + totalCount + ")");
				}
				System.out.println("[RETRY] End. Total: " + totalCount + ", Success: " + (totalCount - failCount) + ", Fail: " + failCount);
				
			} else {
				System.out.println("[ERROR] Request failed. (BatchUpdateException UpdateCounts == null)");
			}
		}
		
	}
	
	@Test
	public void testHandleGpsData() throws UnsupportedEncodingException, IOException {
		setKinesisRequestDataFromJson(event, "/samples/gps_event.json");
		TestContext ctx = new TestContext();
		ctx.setInvokedFunctionArn(TestContext.SAMPLE_ARN_LOCAL);
		gatewayConsumer.handleRequest(event, ctx);
	}
	
	@Test
	public void testHandleGpsDataBulk() throws UnsupportedEncodingException, IOException {
		
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		Stat stat = new Stat();
		stat.setAcconTime(20180903051051L);
		stat.setCurrentTripMileage(200);
		stat.setCurrentFuel(1000);
		stat.setDeviceTime(20180903054908L);
		
		Gps gps = new Gps();
		gps.setSpeed(250);
		gps.setDirection(1840);
		gps.setLatitude(87784439);
		gps.setLongitude(416811332);
		
		GpsDataDao dao = new GpsDataDao();
		dao.addGpsDataToInsertQueue("ETEST000936", stat, gps, 360, 0);
		dao.addGpsDataToInsertQueue("ETEST000937", stat, gps, 370, 0);
		dao.addGpsDataToInsertQueue("ETEST000938", stat, gps, 380, 0);
		dao.addGpsDataToInsertQueue("ETEST000939", stat, gps, 390, 0);
		dao.addGpsDataToInsertQueue("ETEST000940", stat, gps, 400, 0);
		
		dao.batchInsertWithRetry();
	}
	
//	@Test
//	public void testBatchUpdateTripEndDate() throws UnsupportedEncodingException, IOException {
//		
//		Env.init(TestContext.SAMPLE_ARN_PROD);
//		
//		TripTempDao dao = new TripTempDao();
//		int startDate = 20180919;
//		int endDate = 20180920;
//		
//		for(int dd = startDate; dd < endDate; dd++) {
//			
//			System.out.println("Start batch processing. Date: " + dd);
//			
//			for (int hh = 0; hh < 24; hh++) {
//				String startDateHour = String.format(dd + "%02d0000", hh);
//				String endDateHour = String.format(dd + "%02d0000", hh + 1);
//				
//				dao.getTripAndAddToQueue(startDateHour, endDateHour);
//				dao.batchUpdate();
//			}
//			
//			System.out.println("End batch processing. Date: " + dd);
//		}
//		
//	}
	
	@Test
	public void testHandleLoginData() throws UnsupportedEncodingException, IOException {
		setKinesisRequestDataFromJson(event, "/samples/login_event.json");
		gatewayConsumer.handleRequest(event , new TestContext());
	}
	
	@Test
	public void testHandleEngineParamConfigStatus() throws UnsupportedEncodingException, IOException {
		setKinesisRequestDataFromJson(event, "/samples/engine_param_config_status.json");
		gatewayConsumer.handleRequest(event , new TestContext());
	}
	
	@Test
	public void testHandleLogoutData() throws UnsupportedEncodingException, IOException {
		setKinesisRequestDataFromJson(event, "/samples/logout_event.json");
		gatewayConsumer.handleRequest(event , new TestContext());
	}
	
	@Test
	public void testHandleFaultPassengerData() throws UnsupportedEncodingException, IOException {
		setKinesisRequestDataFromJson(event, "/samples/fault_passenger_event.json");
		TestContext ctx = new TestContext();
		ctx.setInvokedFunctionArn(TestContext.SAMPLE_ARN_LOCAL);
		gatewayConsumer.handleRequest(event, ctx);
	}

	
	private void setKinesisRequestDataFromJson(KinesisEvent event, String jsonPath) throws IOException {
		event.getRecords().get(0).getKinesis().setData(loadSampleData(jsonPath));
	}

	// Create a ProtoBuf data from JSON file.
	@SuppressWarnings("unused")
	private ByteBuffer createProtoBufData(String jsonPath) throws IOException {
		
		String jsonStr = IOUtils.toString(this.getClass().getResourceAsStream(jsonPath));
		
		JsonMessages.Builder aggregatedRecordList = JsonMessages.newBuilder();
		JsonMessage.Builder record = JsonMessage.newBuilder();
		record.setJsonBody(jsonStr);
		aggregatedRecordList.addJsonMessage(record.build());

		return ByteBuffer.wrap(aggregatedRecordList.build().toByteArray());
	}
		
	// Create a ProtoBuf data from JSON file.
	private ByteBuffer loadSampleData(String jsonPath) throws IOException {
		String jsonStr = IOUtils.toString(this.getClass().getResourceAsStream(jsonPath));
		return ByteBuffer.wrap(jsonStr.getBytes());
	}
	
	
	@SuppressWarnings("unused")
	private void setKinesisRequestDataFromJsonBulk(KinesisEvent event, String jsonPath) throws IOException {
		event.getRecords().get(0).getKinesis().setData(createProtoBufDataBulk(jsonPath));
	}
	
	// Create a ProtoBuf data from JSON file.
	private ByteBuffer createProtoBufDataBulk(String jsonPath) throws IOException {
		
		JsonMessages.Builder aggregatedRecordList = JsonMessages.newBuilder();
		String jsonStr = IOUtils.toString(this.getClass().getResourceAsStream(jsonPath));
		JsonNode json = mapper.readTree(jsonStr);
		
		for (int i = 0; i < json.size(); i++) {
			JsonMessage.Builder record = JsonMessage.newBuilder();
			record.setJsonBody(json.get(i).toString());
			aggregatedRecordList.addJsonMessage(record.build());
		}
		
		return ByteBuffer.wrap(aggregatedRecordList.build().toByteArray());
	}
	

	private GpsData createGpsData(String devId, Stat stat, Gps gps, int rpm, int isHistory) {
		GpsData gpsData = new GpsData();
		gpsData.setDevId(devId);
		gpsData.setAcconTime(stat.getAcconTime());
		gpsData.setTmTime(gps.getTmTime());
		gpsData.setSpeed(gps.getSpeed());
		gpsData.setDirection((short) gps.getDirection());
		gpsData.setLat(gps.getLatitude());
		gpsData.setLng(gps.getLongitude());
		gpsData.setRpm(rpm);
		gpsData.setMileage((int) stat.getCurrentTripMileage());
		gpsData.setFuelWear(stat.getCurrentFuel());
		gpsData.setGpsTime(gps.getDevTime());
		gpsData.setIsHistory(isHistory);
		gpsData.setIsLocated(gps.getLocationStatus());
		
		return gpsData;
	}

}
