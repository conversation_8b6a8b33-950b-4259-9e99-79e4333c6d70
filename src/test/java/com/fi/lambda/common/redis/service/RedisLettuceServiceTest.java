package com.fi.lambda.common.redis.service;

import java.util.Map;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fi.lambda.common.TestContext;
import com.fi.lambda.model.DashboardCache;
import com.fi.lambda.model.HosHistoryCache;
import com.fleetup.rds.Env;
import org.junit.contrib.java.lang.system.EnvironmentVariables;

public class RedisLettuceServiceTest {
	@Rule
	public EnvironmentVariables env = new EnvironmentVariables();

	private ObjectMapper mapper = new ObjectMapper()
			.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	private LettuceService lettuceService;
	
	@Before
	public void setUp() throws Exception {
		env.set("LOG_LEVEL", "debug");
		Env.init(TestContext.SAMPLE_ARN_LOCAL);
		if (lettuceService == null){
            lettuceService = new LettuceService();
            try {
                lettuceService.initConnectionFactory();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
//		mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
//		event = mapper.readValue(this.getClass().getResourceAsStream("/samples/kinesis_event.json"), new TypeReference<SNSEvent>() {});
	}
	
	@Test
	public void testGetField() {
		String devId = "dev692";
		String odometerStr = lettuceService.hgetSync(devId, "odometer");
		double odometer = isDouble(odometerStr) ? Double.parseDouble(odometerStr) : 0;
		System.out.println(odometer);
	}
	
	@Test
	public void testToString() {
		Map<String, String> map = lettuceService.hgetallSync("3NF2019000940");
        DashboardCache cache = mapper.convertValue(map, DashboardCache.class);
        DashboardCache saveToRedis = cache != null
				? DashboardCache.builder()
					.gatewayId(cache.getGatewayId())
					.acconTime(cache.getAcconTime())
					.tmTime(cache.getTmTime())
					.tripMileage(cache.getTripMileage())
					.fuelWear(cache.getFuelWear())

					.status(cache.getStatus())
					.totalFuelConsu(cache.getTotalFuelConsu())
					.deviceMileage(cache.getDeviceMileage())
					.startTime(cache.getStartTime())
					.endTime(cache.getEndTime())
					.lastUpdateTime(cache.getLastUpdateTime())
					.engineParamConfigStatus(cache.getEngineParamConfigStatus())
					.speed(cache.getSpeed())
					.direction(cache.getDirection())
					.lat(cache.getLat())
					.lng(cache.getLng())
					.deviceTime(cache.getDeviceTime())
					.odometer(cache.getOdometer())
					.rpm(cache.getRpm())
					.ifta(cache.getIfta())

					.eldStatus(cache.getEldStatus())
					.eldCount(cache.getEldCount())
					.eldTmTime(cache.getEldTmTime())
					.eldIdleTmTime(cache.getEldIdleTmTime())
					.hasDriven(cache.getHasDriven())

					.dupCount(cache.getDupCount())
					.lastGpsUpdateTime(cache.getLastGpsUpdateTime())
					.build()
				: new DashboardCache();
        
        System.out.println(cache == null);
        System.out.println(cache.getTmTime() == null);
        System.out.println(saveToRedis.toString());
	}

	@Test
	public void testSaveHosCache() throws InterruptedException {
		String hosHistoryTripKey = String.format("HOSHISTORY:%s:%s", "213NW1000000544", 20220324224618L);

		HosHistoryCache hosHistoryCache = new HosHistoryCache();
		hosHistoryCache.setStartTime(20220324224618L);
		hosHistoryCache.setEndTime(20220324224618L);

		lettuceService.hmsetAsync(hosHistoryTripKey, hosHistoryCache);

		Thread.sleep(2000L);
		Map<String, String> map = lettuceService.hgetallSync(hosHistoryTripKey);
		System.out.println(map);
	}
	
	@Test
	public void testGetHosCache() {
		String hosHistoryTripKey = String.format("HOSHISTORY:%s:%s", "213NW1000000544", 20220324224618L);

		Map<String, String> map = lettuceService.hgetallSync(hosHistoryTripKey);
		HosHistoryCache cache = mapper.convertValue(map, HosHistoryCache.class);
		System.out.println(map);
		System.out.println(cache);
		System.out.println("startTime=" + cache.getStartTime() + ", endTime=" + cache.getEndTime() + ", lastUpdateTime=" + cache.getLastUpdateTime());
	}
	
	@Test
	public void testUpdateLivaEtaAsync() {
		String liveEtaDevKey = "yentest001";
		long acconTime = 20220331120020L;
		long startTime = 20220331123555L;
		lettuceService.updateLivaEtaAsync(liveEtaDevKey, acconTime, startTime);
	}
	
	/**
	 * Check if a string be parsed into double.
	 * @param str
	 * @return
	 */
	private boolean isDouble(String str) {
		try {
			Double.parseDouble(str);
		} catch (NullPointerException | NumberFormatException e) {
			return false;
		}
		
		return true;
	}
}
