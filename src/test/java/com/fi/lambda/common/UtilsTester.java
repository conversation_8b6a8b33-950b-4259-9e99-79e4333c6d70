package com.fi.lambda.common;

import com.fi.lambda.utils.DateTimeUtils;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;

import java.text.DecimalFormat;

import static org.junit.Assert.assertEquals;

@Log4j2
public class UtilsTester {

	@Test
	public void testIsValid() {
		long tmTime = 20200302212413L;
		long pastTime = 20190902212413L;
		long futureTime = 20200302222413L;
		assertEquals(true, DateTimeUtils.isValid(tmTime, pastTime, futureTime));
	}
	
	@Test
	public void testGetPastTime() {
		long pastTime = DateTimeUtils.getPastUTC(6, DateTimeUtils.DTF_yyyyMMddHHmmss);
		System.out.println(pastTime);
	}

	@Test
	public void testOdometer() {
		DecimalFormat df = new DecimalFormat("#.####"); // '%.4f' in c++
		
		float odometer = 536870912;
		
		System.out.println(df.format(odometer));
	}
	
	@Test
	public void testAddSecond() {
		long longValue = 20201010010159L;
		long result = DateTimeUtils.addSecond(longValue , 1, DateTimeUtils.yyyyMMddHHmmss);
		assertEquals(result, 20201010010200L);
		
		longValue = 20201010010260L; // wrong input, but it should work.
		result = DateTimeUtils.addSecond(longValue , 1, DateTimeUtils.yyyyMMddHHmmss);
		assertEquals(result, 20201010010301L);
	}

	@Test
	public void testException() {
		try {
			throw new NullPointerException("TEST");
		} catch (Exception e) {
			log.error("Failed!", e);
		}
	}
}
