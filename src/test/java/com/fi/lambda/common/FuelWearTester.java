package com.fi.lambda.common;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.PrimitiveIterator;
import java.util.Random;
import java.util.stream.IntStream;

import org.junit.Assert;
import org.junit.Test;

import com.fi.lambda.handler.GatewayOBDConsumer;

public class FuelWearTester {

	private GatewayOBDConsumer consumer = new GatewayOBDConsumer();
	private static final String FUEL_WEAR_METHOD = "getActualFuel";
	private static final int FUEL_MAX = 65_535;

	private Integer invokeGetActualFuel(Integer prevFw, Long prevMileage, Long prevTmTime, Integer curFw,
			Long curMileage, Long curTmTime) {
		try {
			Method method = GatewayOBDConsumer.class.getDeclaredMethod(FUEL_WEAR_METHOD, Integer.class, Long.class,
					Long.class, Integer.class, Long.class, Long.class);
			method.setAccessible(true);
			return (Integer) method.invoke(consumer, prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);
		} catch (NoSuchMethodException | SecurityException | IllegalAccessException | IllegalArgumentException
				| InvocationTargetException e) {
			e.printStackTrace();
		}

		return null;
	}
	
	private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
	
	private long addSecond(long tmTime, int second) {
		Calendar c = Calendar.getInstance();
		try {
			c.setTime(dateFormat.parse(Long.toString(tmTime)));
			c.add(Calendar.SECOND, second);
			String nextDatetime = dateFormat.format(c.getTime());
			return Long.parseLong(nextDatetime);
		} catch (ParseException | NumberFormatException e) {
			e.printStackTrace();
		}
		
		return 0L;
	}
	
	private long addHour(long tmTime, int hour) {
		Calendar c = Calendar.getInstance();
		try {
			c.setTime(dateFormat.parse(Long.toString(tmTime)));
			c.add(Calendar.HOUR, hour);
			String nextDatetime = dateFormat.format(c.getTime());
			return Long.parseLong(nextDatetime);
		} catch (ParseException | NumberFormatException e) {
			e.printStackTrace();
		}
		
		return 0L;
	}
	
	@Test
	public void testPrevEdgeCases() {
		long prevTmTime = 20200604210842L;
		Assert.assertEquals(FUEL_MAX, invokeGetActualFuel(30_000, 2_500_000L, 20200604210842L, 0, 5_000_000L, addHour(prevTmTime, 30)).intValue());
		Assert.assertEquals(FUEL_MAX, invokeGetActualFuel(30_001, 2_500_000L, 20200604210842L, 0, 5_000_000L, addHour(prevTmTime, 30)).intValue());
		Assert.assertEquals(30_000, invokeGetActualFuel(0, 0L, 20200604210842L, 30_000, 2_500_000L, addHour(prevTmTime, 30)).intValue());
		Assert.assertEquals(30_001, invokeGetActualFuel(0, 0L, 20200604210842L, 30_001, 2_500_000L, addHour(prevTmTime, 30)).intValue());
	}
	
	@Test
	public void testRollOver() {
		int prevFw = 65_519;
		long prevMileage = 1_415_424;
		long prevTmTime = 20200609_145952L;
		
		int curFw = 33;
		long curMileage = 1_417_216;
		long curTmTime = 20200609_150052L;
		
		int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);
		
		Assert.assertEquals(65_568, result);
	}
	
	@Test
	public void testStartWithIdling() {
		int prevFw = 0;
		long prevMileage = 0;
		long prevTmTime = 20201105_121726L;
		
		int curFw = 2_350;
		long curMileage = 64;
		long curTmTime = 20201105_121736L;
		
		int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);
		
		Assert.assertEquals(2_350, result);
	}
	
	@Test
	public void testEndWithZero() {
		int prevFw = 2_550;
		long prevMileage = 79_648;
		long prevTmTime = 20200522_130809L;
		
		int curFw = 0;
		long curMileage = 79_648;
		long curTmTime = 20200522_130819L;
		
		int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);
		
		Assert.assertEquals(2_550, result);
	}
	
	@Test
	public void testMalfunctionedDevice() {
		int prevFw = 0;
		long prevMileage = 0;
		long prevTmTime = 20200522_130809L;
		
		Random random = new Random();
		IntStream limitedIntStream = random.ints(8, 5_000, FUEL_MAX-1);
		
		PrimitiveIterator.OfInt iterator = limitedIntStream.iterator();
		
		while (iterator.hasNext()) {
			int curFw = iterator.nextInt();
			long curTmTime = addSecond(prevTmTime, 10);
			int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, 0L, curTmTime);
			Assert.assertEquals(0, result);
			
			prevFw = result;
			prevTmTime = curTmTime;
		}
	}
	
	@Test
	public void testIdling() {
		int prevFw = 0;
		long prevMileage = 79_648;
		long prevTmTime = 20200522_130809L;
		
		long curMileage = prevMileage;
		long curTmTime = addSecond(prevTmTime, 10);
		
		Assert.assertEquals(50, invokeGetActualFuel(prevFw, prevMileage, prevTmTime, 50, curMileage, curTmTime).intValue());
		Assert.assertEquals(0, invokeGetActualFuel(prevFw, prevMileage, prevTmTime, 100, curMileage, curTmTime).intValue());
	}

	/**
	 * The tests below were created per QA-Ganesh's request based on 
	 * https://docs.google.com/spreadsheets/d/1kN-ojmrzC4tqRUg9VcBeDDEzYvY2uJjHNTuZkWTVE0Y/edit?usp=sharing
	 */
	@Test
	public void testGradualIncrease() {
		int prevFw = 0;
		long prevMileage = 0;
		long prevTmTime = 20200604210842L;
		
		int expectedFw = prevFw;
		
		while (prevFw <= 70_000) {
			expectedFw = prevFw + 100;
			int curFw = expectedFw >= FUEL_MAX ? expectedFw - FUEL_MAX : expectedFw;
			long curMileage = prevMileage + 5000;
			long curTmTime = addSecond(prevTmTime, 10);
			
			int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);

//			System.out.println(
//					String.format("[prevTmTime=%s, prevFw=%s, prevMileage=%s], [curTmTime=%s, curFw=%s, curMileage=%s], Result=%s",
//							prevTmTime, prevFw, prevMileage, curTmTime, curFw, curMileage, result));
			Assert.assertEquals(expectedFw, result);

			prevFw = result;
			prevMileage = curMileage;
			prevTmTime = curTmTime;
		}
	}
	
	@Test
	public void testHugeJump() {
		int prevFw = 0;
		long prevMileage = 0;
		long prevTmTime = 20200604210842L;
		
		int expectedFw = prevFw;
		
		while (prevFw <= 15_000) {
			expectedFw = prevFw + 100;
			int curFw = expectedFw >= FUEL_MAX ? expectedFw - FUEL_MAX : expectedFw;
			long curMileage = prevMileage + 5000;
			long curTmTime = addSecond(prevTmTime, 10);
			
			int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);

//			System.out.println(
//					String.format("[prevTmTime=%s, prevFw=%s, prevMileage=%s], [curTmTime=%s, curFw=%s, curMileage=%s], Result=%s",
//							prevTmTime, prevFw, prevMileage, curTmTime, curFw, curMileage, result));
			Assert.assertEquals(expectedFw, result);

			prevFw = result;
			prevMileage = curMileage;
			prevTmTime = curTmTime;
		}
		
		expectedFw = 80_000;
		int curFw = expectedFw - FUEL_MAX;
		long curMileage = prevMileage + 3_250_000;
		long curTmTime = addHour(prevTmTime, 30);
		
		int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);

//		System.out.println(
//				String.format("[prevTmTime=%s, prevFw=%s, prevMileage=%s], [curTmTime=%s, curFw=%s, curMileage=%s], Result=%s",
//						prevTmTime, prevFw, prevMileage, curTmTime, curFw, curMileage, result));
		Assert.assertEquals(expectedFw, result);
	}
	
	@Test
	public void testIdlingForHours() {
		int prevFw = 0;
		long prevMileage = 0;
		long prevTmTime = 20200604210842L;
		
		int expectedFw = prevFw;
		
		while (prevFw <= 60_000) {
			expectedFw = prevFw + 100;
			int curFw = expectedFw >= FUEL_MAX ? expectedFw - FUEL_MAX : expectedFw;
			long curMileage = prevMileage + 5000;
			long curTmTime = addSecond(prevTmTime, 10);
			
			int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, curMileage, curTmTime);

//			System.out.println(
//					String.format("[prevTmTime=%s, prevFw=%s, prevMileage=%s], [curTmTime=%s, curFw=%s, curMileage=%s], Result=%s",
//							prevTmTime, prevFw, prevMileage, curTmTime, curFw, curMileage, result));
			Assert.assertEquals(expectedFw, result);

			prevFw = result;
			prevMileage = curMileage;
			prevTmTime = curTmTime;
		}
		
		while (prevFw <= FUEL_MAX + 3) {
			expectedFw = prevFw + 2;
			int curFw = expectedFw >= FUEL_MAX ? expectedFw - FUEL_MAX : expectedFw;
			long curTmTime = addSecond(prevTmTime, 10);
			
			int result = invokeGetActualFuel(prevFw, prevMileage, prevTmTime, curFw, prevMileage, curTmTime);

//			System.out.println(
//					String.format("[prevTmTime=%s, prevFw=%s, prevMileage=%s], [curTmTime=%s, curFw=%s, curMileage=%s], Result=%s",
//							prevTmTime, prevFw, prevMileage, curTmTime, curFw, prevMileage, result));
			Assert.assertEquals(expectedFw, result);

			prevFw = result;
			prevTmTime = curTmTime;
		}
	}
}
