package com.fi.lambda.common.aws;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.fi.lambda.common.TestContext;
import com.fleetup.rds.Env;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SQSServiceTest {

    public static final String DEV_ID = "2XNSDSDA12345780";

    private SQSService sqsService;

    @Mock
    private AmazonSQS sqs;

    @Before
    public void setUp() throws Exception {
    	Env.init(TestContext.SAMPLE_ARN_STRESS);
        when(this.sqs.sendMessageBatch(any())).thenReturn(new SendMessageBatchResult());
        sqsService = new SQSService(sqs);
        sqsService.setQueueUrl(Env.getAccountId(), Env.getRegion(), Env.getStage(), "-trip-conversion");
    }

    @Test
    public void testSetQueueUrl() {
        String queueUrl = sqsService.getQueueUrl();
        Assert.assertEquals("https://sqs.us-west-2.amazonaws.com/************/stress-trip-conversion", queueUrl);
    }
    
    @Test
    public void testAddToMessageList() {
        sqsService.addToMessageList(DEV_ID, "1", "1", "1", "1");
        sqsService.sendBatchMessageToSQS();
        verify(this.sqs, times(1)).sendMessageBatch(any());
    }


}
