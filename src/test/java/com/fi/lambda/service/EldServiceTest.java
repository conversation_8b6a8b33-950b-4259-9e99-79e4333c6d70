package com.fi.lambda.service;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import com.fi.gateway.producer.event.Gps;
import com.fi.lambda.common.aws.SQSService;
import com.fi.lambda.model.DashboardCache;
import com.fi.lambda.model.FmcsaEvent;
import com.fi.lambda.utils.DateTimeUtils;

@RunWith(MockitoJUnitRunner.class)
public class EldServiceTest {
	
	@InjectMocks
	private FmcsaService fmcsaService;
	
	@Mock
	private SQSService eldSqsService;

	private DashboardCache prevCache;
	private DashboardCache cache;
	private Gps[] gps;
	
	@Captor
	ArgumentCaptor<FmcsaEvent> eldValueCaptor;
	
	private final static String DEV_ID = "testDevId";
	private final static long START_ELD_TIME = 20200210120010L;
	private final static long TM_TIME = 20200210120110L;
	private final static long HOUR_LATER_TM_TIME = 20200210130010L;
	private final static long FIVE_MIN_TM_TIME = 20200210120510L;
	
	private static final int SPEED_THRESHOLD = 220; // Management team decided to use 220 cm/s for 5 mph threshold.
	
	// Driving Status
	private static final short NIM = 0; // Not-in-motion status
	private static final short FIVE_MIN = 1; // Not-in-motion and 5-minute idling event was sent
	private static final short ONE_MIN = 2; // No response more than 1 minute after 5-minute idling event was sent
	private static final short IM = 5; // Regular in-motion status
	private static final short IM_FLAG_FIVE_MIN = 6; // In-motion status with flag to indicates the previous status was FIVE_MIN
	private static final short IM_FLAG_ONE_MIN = 7; // In-motion status with flag to indicates the previous status was ONE_MIN
	
	// ELD Event Type
	private static final int ENGINE_ON = 1;
	private static final int DRIVING = 2;
	private static final int INTERMEDIATE_DRIVING = 3;
	private static final int NOT_IN_MOTION = 4;
	private static final int IDLING = 5;
	private static final int ENGINE_OFF = 6;
	private static final int SIGNAL_BACK = 7;
	
	private static final short DATAPOINT_THRESHOLD = 2; // Change status when receive this number of datapoints.
	
	@Before
	public void setUp() {
		doNothing().when(eldSqsService).addToMessageList(eldValueCaptor.capture());
		prevCache = DashboardCache.builder()
				.tmTime(START_ELD_TIME)
				.status(1)
				.build();
		cache = DashboardCache.builder()
				.eldStatus(NIM)
				.eldCount((short) 0)
				.eldTmTime(START_ELD_TIME)
				.eldIdleTmTime(START_ELD_TIME)
				.hasDriven((short) 0)
				.build();

		fmcsaService.setHosSQSService(mock(SQSService.class));
		
		gps = new Gps[1];
		Gps curGps = new Gps();
		curGps.setTmTime(TM_TIME);
		curGps.setSpeed(SPEED_THRESHOLD);
		curGps.setLatitude(84534978L);
		curGps.setLongitude(-380967708L);
		gps[0] = curGps;
	}
	
	@Test
	public void testNewTrip() {
		Gps startGps = new Gps();
		startGps.setTmTime(TM_TIME);
		startGps.setSpeed(SPEED_THRESHOLD);
		startGps.setLatitude(84534978L);
		startGps.setLongitude(-380967708L);
		
		cache.setTmTime(TM_TIME);
		
		fmcsaService.startNewTrip(DEV_ID, cache, false, startGps);
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));

		assertEquals(NIM, cache.getEldStatus().shortValue());
		assertEquals(0, cache.getEldCount().shortValue());
		assertEquals(TM_TIME, cache.getEldTmTime().longValue());
		assertEquals(0, cache.getHasDriven().shortValue());
		assertEquals(ENGINE_ON, resultMap.getEventType().intValue());
	}
	
	@Test
	public void testDrivingEvent() {
		cache.setTmTime(TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD);
		cache.setEldStatus(IM);
		cache.setEldCount((short) (DATAPOINT_THRESHOLD - 1));
		
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		
		assertEquals(IM, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD, cache.getEldCount().shortValue());
		assertEquals(TM_TIME, cache.getEldTmTime().longValue());
		assertEquals(1, cache.getHasDriven().shortValue());
		assertEquals(DRIVING, resultMap.getEventType().intValue());
	}
	
	@Test
	public void testDriving() {
		cache.setTmTime(TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD);
		cache.setEldStatus(IM);
		cache.setEldCount((short) (DATAPOINT_THRESHOLD + 1));
		
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		
		verify(eldSqsService, times(0)).addToMessageList(any(Object.class));
		assertEquals(IM, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD + 2, cache.getEldCount().shortValue());
		assertEquals(START_ELD_TIME, cache.getEldTmTime().longValue());
	}
	
	@Test
	public void testIntermediateDrivingEvent() {
		cache.setTmTime(HOUR_LATER_TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD);
		cache.setEldStatus(IM);
		cache.setEldCount(DATAPOINT_THRESHOLD);
		
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		
		assertEquals(IM, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD, cache.getEldCount().shortValue());
		assertEquals(HOUR_LATER_TM_TIME, cache.getEldTmTime().longValue());
		assertEquals(INTERMEDIATE_DRIVING, resultMap.getEventType().intValue());
	}
	
	@Test
	public void testNotInMotionEvent() {
		cache.setTmTime(TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD-1);
		cache.setEldStatus(NIM);
		cache.setHasDriven((short) 1);
		cache.setEldCount((short) (DATAPOINT_THRESHOLD - 1));
		
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		
		assertEquals(NIM, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD, cache.getEldCount().shortValue());
		assertEquals(START_ELD_TIME, cache.getEldTmTime().longValue());
		assertEquals(NOT_IN_MOTION, resultMap.getEventType().intValue());
	}
	
	@Test
	public void testIdlingEvent() {
		cache.setEldIdleTmTime(START_ELD_TIME);
		cache.setTmTime(FIVE_MIN_TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD-1);
		cache.setEldStatus(NIM);
		cache.setHasDriven((short) 1);
		cache.setEldCount(DATAPOINT_THRESHOLD);
		
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		
		assertEquals(FIVE_MIN, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD + 1, cache.getEldCount().shortValue());
		assertEquals(START_ELD_TIME, cache.getEldTmTime().longValue());
		assertEquals(FIVE_MIN_TM_TIME, cache.getEldIdleTmTime().longValue());
		assertEquals(IDLING, resultMap.getEventType().intValue());
	}
	
	/**
	 * Test rapid status change for a new trip
	 */
	@Test
	public void testNonDrivenChangingStatus() {
		cache.setEldStatus(NIM);
		long prevTmTime = TM_TIME;
		
		for (int i = 1; i < DATAPOINT_THRESHOLD; i++) {
			cache.setTmTime(DateTimeUtils.addSecond(TM_TIME, i * 10, DateTimeUtils.yyyyMMddHHmmss));
			cache.setSpeed(SPEED_THRESHOLD);
			prevCache.setTmTime(prevTmTime);
			fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
			prevTmTime = cache.getTmTime();
		}
		
		for (int i = 1; i <= DATAPOINT_THRESHOLD; i++) {
			cache.setTmTime(DateTimeUtils.addSecond(TM_TIME, (DATAPOINT_THRESHOLD + i) * 10, DateTimeUtils.yyyyMMddHHmmss));
			cache.setSpeed(SPEED_THRESHOLD-1);
			prevCache.setTmTime(prevTmTime);
			fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
			prevTmTime = cache.getTmTime();
		}
		
		verify(eldSqsService, times(0)).addToMessageList(any(Object.class));
		
		assertEquals(NIM, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD, cache.getEldCount().shortValue());
		assertEquals(START_ELD_TIME, cache.getEldTmTime().longValue());
	}
	
	/**
	 * Test rapid status change when the 5-minute idling event was sent.
	 */
	@Test
	public void testIdlingChangeStatus() {
		cache.setEldStatus(FIVE_MIN);
		cache.setEldIdleTmTime(TM_TIME);
		cache.setHasDriven((short) 1);
		cache.setEldCount(DATAPOINT_THRESHOLD);
		long prevTmTime = FIVE_MIN_TM_TIME;
		
		for (int i = 1; i < DATAPOINT_THRESHOLD; i++) {
			cache.setTmTime(DateTimeUtils.addSecond(FIVE_MIN_TM_TIME, i * 10, DateTimeUtils.yyyyMMddHHmmss));
			cache.setSpeed(SPEED_THRESHOLD);
			prevCache.setTmTime(prevTmTime);
			fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
			prevTmTime = cache.getTmTime();
		}
		
		cache.setTmTime(FIVE_MIN_TM_TIME + (DATAPOINT_THRESHOLD * 10));
		cache.setSpeed(SPEED_THRESHOLD-1);
		prevCache.setTmTime(prevTmTime);
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		
		verify(eldSqsService, times(0)).addToMessageList(any(Object.class));
		
		assertEquals(FIVE_MIN, cache.getEldStatus().shortValue());
		assertEquals(DATAPOINT_THRESHOLD, cache.getEldCount().shortValue());
		assertEquals(START_ELD_TIME, cache.getEldTmTime().longValue());
	}
	
	@Test
	public void testIdlingToDivingFrequentChange() {
		cache.setEldStatus(FIVE_MIN);
		cache.setEldIdleTmTime(TM_TIME);
		cache.setHasDriven((short) 1);
		cache.setEldCount(DATAPOINT_THRESHOLD);
		long prevTmTime = FIVE_MIN_TM_TIME;
		
		for (int i = 1; i < DATAPOINT_THRESHOLD; i++) {
			cache.setTmTime(DateTimeUtils.addSecond(FIVE_MIN_TM_TIME, i * 10, DateTimeUtils.yyyyMMddHHmmss));
			cache.setSpeed(SPEED_THRESHOLD-1);
			prevCache.setTmTime(prevTmTime);
			fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
			prevTmTime = cache.getTmTime();
		}
		
		for (int i = 1; i <= DATAPOINT_THRESHOLD; i++) {
			cache.setTmTime(DateTimeUtils.addSecond(FIVE_MIN_TM_TIME, (DATAPOINT_THRESHOLD + i) * 10, DateTimeUtils.yyyyMMddHHmmss));
			cache.setSpeed(SPEED_THRESHOLD);
			prevCache.setTmTime(prevTmTime);
			fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
			prevTmTime = cache.getTmTime();
		}
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		assertEquals(IM, cache.getEldStatus().shortValue());
		assertEquals(DRIVING, resultMap.getEventType().intValue());
	}
	
	@Test
	public void testSignalBack() {
		cache.setTmTime(TM_TIME);
		cache.setSpeed(SPEED_THRESHOLD);
		cache.setEldStatus(IM);
		cache.setEldCount(DATAPOINT_THRESHOLD);
		prevCache.setStatus(2);
		fmcsaService.processOngoingTrips(DEV_ID, cache, prevCache, false);
		
		FmcsaEvent resultMap = eldValueCaptor.getValue();
		
		verify(eldSqsService, times(1)).addToMessageList(any(Object.class));
		
		assertEquals(IM, cache.getEldStatus().shortValue());
		assertEquals(SIGNAL_BACK, resultMap.getEventType().intValue());
	}
}
