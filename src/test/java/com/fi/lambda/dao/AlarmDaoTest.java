package com.fi.lambda.dao;

import com.fi.lambda.common.Constants;
import com.fi.lambda.model.DtcData;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.fi.lambda.common.TestContext;
import com.fi.lambda.model.AlertNotification;
import com.fi.lambda.model.DtcCarType;
import com.fleetup.rds.Env;

public class AlarmDaoTest {
	AlarmDao alarmDao;
	
	@Before
	public void setUp() throws Exception {
		alarmDao = new AlarmDao();
	}
	
	@Test
	@Ignore
	//TODO use mocks
	public void testGetTripStatData() {
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		int happenTimes = 1;
		DtcData dtcData = new DtcData("testDevId123", 20170602231700L, 20170602231700L, happenTimes, DtcCarType.PASSENGER, 123L);
		long alarmDataId = alarmDao.insertToDtcData(dtcData, Constants.DTC_STATUS.DTC_STORED);
		//assertThat(alarmDataId,greaterThanOrEqualTo(1L));
		System.out.println(alarmDataId);
	}

}
