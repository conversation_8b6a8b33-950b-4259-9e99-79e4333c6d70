package com.fi.lambda.dao;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.fi.lambda.common.TestContext;
import com.fi.lambda.model.GpsData;
import com.fleetup.rds.Env;

public class GpsDataDaoTest {
	GpsDataDao gpsDataDao;
	
	@Before
	public void setUp() throws Exception {
		gpsDataDao = new GpsDataDao();
	}
	
	@Test
	@Ignore
	//TODO use mocks
	public void testQueryAlertDeviceByCond4Export() {
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		List<GpsData> gpsDataList = new ArrayList<GpsData>();
		
		GpsData gpsData = new GpsData();
		gpsData.setDevId("testDevId1234");
		gpsData.setAcconTime(20170602231700L);
		gpsData.setTmTime(20170602231700L);
		gpsData.setSpeed(30);
		gpsData.setDirection((short)5);
		gpsData.setLat(135302980L);
		gpsData.setLng(457138797L);
		gpsData.setRpm(30);
		gpsData.setMileage((int) 100);
		gpsData.setFuelWear(1000);
		gpsData.setGpsTime(20170602231700L);   
		gpsData.setIsHistory(0); 
		gpsData.setIsLocated(3);
		gpsDataList.add(gpsData);
		
		gpsData = new GpsData();
		gpsData.setDevId("testDevId1234");
		gpsData.setAcconTime(20170602231700L);
		gpsData.setTmTime(20170602231800L);
		gpsData.setSpeed(31);
		gpsData.setDirection((short)5);
		gpsData.setLat(135302980L);
		gpsData.setLng(457138797L);
		gpsData.setRpm(30);
		gpsData.setMileage((int) 100);
		gpsData.setFuelWear(2000);
		gpsData.setGpsTime(20170602231800L);   
		gpsData.setIsHistory(1); 
		gpsData.setIsLocated(3); 
		gpsDataList.add(gpsData);
		
		gpsDataDao.insertToGpsData(gpsDataList);
		//fail("Not yet implemented");
	}

}
