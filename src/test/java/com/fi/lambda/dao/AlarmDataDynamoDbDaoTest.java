package com.fi.lambda.dao;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Set;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class AlarmDataDynamoDbDaoTest {

    public static final String DEV_ID = "2XNM12345678";
    public static final String TABLE_NAME = "stress-alarmdata-202001";
    public static final long TM_TIME = 20200101020000L;

    @Mock
    private DynamoDBMapper mapper;

    private AlarmDataDynamoDbDao alarmDataDynamoDbDao;

    @Before
    public void init() {
        alarmDataDynamoDbDao = new AlarmDataDynamoDbDao(mapper);
        alarmDataDynamoDbDao.setTableNamePrefix("stress");
    }

    @Test
    public void testSave() {
        alarmDataDynamoDbDao.addToBatchList(DEV_ID, TM_TIME, TM_TIME, 1, 1, 1, 1);
        alarmDataDynamoDbDao.batchInsertWithRetry();

        ArgumentCaptor<Set> writeBatch = ArgumentCaptor.forClass(Set.class);
        ArgumentCaptor<DynamoDBMapperConfig> config = ArgumentCaptor.forClass(DynamoDBMapperConfig.class);

        verify(this.mapper, times(1)).batchWrite(writeBatch.capture(), any(), config.capture());
        Assert.assertEquals(1, writeBatch.getValue().size());
        Assert.assertEquals(TABLE_NAME, config.getValue().getTableNameOverride().getTableName());
    }
}
