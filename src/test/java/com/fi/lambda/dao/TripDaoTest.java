package com.fi.lambda.dao;

import org.junit.*;

import com.fi.lambda.common.TestContext;
import com.fi.lambda.model.Trip;
import com.fleetup.rds.Env;
import org.junit.contrib.java.lang.system.EnvironmentVariables;

@Ignore
public class TripDaoTest {
	@Rule
	private EnvironmentVariables env = new EnvironmentVariables();

	TripDao tripDao;

	@Before
	public void setUp() throws Exception {
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		env.set("STRESS_SECRET_ARN", "arn:aws:secretsmanager:us-west-2:994093938344:secret:/stress/db/oracle/consumerobd-mTepaw");
		tripDao = new TripDao();
	}

	@Test
	@Ignore
	//TODO use mocks
	public void testGetTripStatData() {
		Trip trip = tripDao.getTripStatData("213GL2015012144", 20200202001200L);
		System.out.println(trip.toString());
	}
	
	@Test
	@Ignore
	//TODO use mocks
	public void testBatchUpdateTable() {
//		tripDao.addTripToUpdateQueue((short) 1, 20191129030900L, 20191129031809L, 2000, 40, "yw20180822b0969", 20191129030845L);
		
		tripDao.batchUpdate();
	}

	@Test
	@Ignore
	//TODO use mocks
	public void testInsertTrip() {
//		insertToTrip(String devId, long acconTime, String startTime, String endTime,
//		long mileage, int fleuWear, int status,double odometer, long totalMileage
		boolean tripInserted = tripDao.insertToTrip("yw20180822b0963", 20230309153041L, "20230309153041", "20230309153051", 10L, 1, 1, 0, 1000);
		System.out.println(tripInserted);
	}
}
