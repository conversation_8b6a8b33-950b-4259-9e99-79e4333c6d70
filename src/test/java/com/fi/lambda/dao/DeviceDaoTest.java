package com.fi.lambda.dao;

import com.fi.lambda.common.TestContext;
import com.fi.lambda.model.DeviceSetting;
import com.fi.lambda.model.OldDevBasicSetting;
import com.fleetup.rds.Env;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

public class DeviceDaoTest {
	DeviceDao deviceDao;

	@Before
	public void setUp() throws Exception {
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		deviceDao = new DeviceDao();
	}

	@Test
	@Ignore
	//TODO use mocks
	public void testGetTripStatData() {
		Env.init(TestContext.SAMPLE_ARN_STRESS);
		OldDevBasicSetting devBasicSetting = new OldDevBasicSetting();
		devBasicSetting.setDevId("");
		devBasicSetting.setOnLine(1);

		deviceDao.insertToDevBasicSetting(devBasicSetting);
	}

	@Test
	@Ignore
	//TODO use mocks
	public void testDeviceExist() {
//		String devId = "213GL1134800064";
		String devId = null;
		DeviceSetting devSetting = deviceDao.deviceExist(devId);
	}

	@Test
	@Ignore
	public void testGetLastEventTime() {
		String devId = "stress00063";
		String lastEventTime = deviceDao.getLastEventTime(devId);
		System.out.println(lastEventTime);
	}

	@Test
	@Ignore
	public void testUpdateLastEventTime() {
		deviceDao.updateConnectionStat("stress00063", "2021-03-09 12:03:23");
	}

	@Test
	@Ignore
	public void saveLastEventTime() {
		deviceDao.saveConnectionStat("stress00063", "2020-02-10 12:03:23");
	}

}
