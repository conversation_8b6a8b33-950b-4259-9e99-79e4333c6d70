package com.fi.lambda.dao;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper.FailedBatch;
import com.amazonaws.services.dynamodbv2.model.ResourceNotFoundException;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.fi.lambda.assertions.MyAssertions;
import com.fi.lambda.model.GpsData;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RunWith(MockitoJUnitRunner.class)
public class GpsDataDdbDaoTest {
	
	@Mock
    private DynamoDBMapper mapper;
	
	private GpsDataDynamoDdbDao gpsDataDynamoDdbDao;
	
	@Before
	public void setUp() throws Exception {
		gpsDataDynamoDdbDao = new GpsDataDynamoDdbDao(mapper);  // for writing to dynamoDB.
		gpsDataDynamoDdbDao.setTableNamePrefix("stress");
	}
	
	/**
	 * Test
	 * - feed the same tmTime from multiple Gps.
	 *  [WARN] FailedBatch, Provided list of item keys contains duplicates (Service: AmazonDynamoDBv2; Status Code: 400; Error Code: ValidationException; Request ID: 8CCDCBNSDK1RDHP1V71UPEHBENVV4KQNSO5AEMVJF66Q9ASUAAJG),stress-gpsdata-201909,{PutRequest: {Item: {devId={S: testDevId1234,}, lng={N: 457138797,}, acconTime={N: 20170602231700,}, gpsTime={N: 20170602231800,}, rpm={N: 30,}, speed={N: 31,}, fuelWear={N: 2000,}, isLocated={N: 3,}, isHistory={N: 1,}, lat={N: 135302980,}, mileage={N: 100,}, tmTime={N: 20190912022317,}, direction={N: 5,}, createDate={S: 2019-09-12T22:44:30.253Z,}}},}
		[WARN] FailedBatch, Provided list of item keys contains duplicates (Service: AmazonDynamoDBv2; Status Code: 400; Error Code: ValidationException; Request ID: 8CCDCBNSDK1RDHP1V71UPEHBENVV4KQNSO5AEMVJF66Q9ASUAAJG),stress-gpsdata-201909,{PutRequest: {Item: {devId={S: testDevId1234,}, lng={N: 457138797,}, acconTime={N: 20170602231700,}, gpsTime={N: 20170602231800,}, rpm={N: 30,}, speed={N: 31,}, fuelWear={N: 2000,}, isLocated={N: 3,}, isHistory={N: 1,}, lat={N: 135302980,}, mileage={N: 100,}, tmTime={N: 20190912022317,}, direction={N: 5,}, createDate={S: 2019-09-12T22:44:30.253Z,}}},}
	 * - feed gpsData with tmTime of which table name does not exist.		
		[WARN] FailedBatch, Requested resource not found (Service: AmazonDynamoDBv2; Status Code: 400; Error Code: ResourceNotFoundException; Request ID: 6IP7LL4Q37K9RT66B3VKECG0D3VV4KQNSO5AEMVJF66Q9ASUAAJG),stress-gpsdata-201706,{PutRequest: {Item: {devId={S: testDevId1234,}, lng={N: 457138797,}, acconTime={N: 20170602231700,}, gpsTime={N: 20170602231700,}, rpm={N: 30,}, speed={N: 300,}, fuelWear={N: 1000,}, isLocated={N: 3,}, isHistory={N: 0,}, lat={N: 135302980,}, mileage={N: 100,}, tmTime={N: 20170610231700,}, direction={N: 5,}, createDate={S: 2019-09-12T22:44:30.253Z,}}},}
	 */
	@Test
	@Ignore
	//TODO use mocks
	public void batchSaveTest() {
//		long yyyymmdd = (20170160124546L / 1000000L);
		
		List<GpsData> gpsDataList = new ArrayList<>();

		// Input
		Date date = new Date(System.currentTimeMillis());
		
		GpsData gpsData = new GpsData();
		gpsData.setId(1L);
		gpsData.setDevId("testDevId1234");
		gpsData.setAcconTime(20170602231700L);
		gpsData.setTmTime(20170610231700L);
		gpsData.setSpeed(300);
		gpsData.setDirection((short)5);
		gpsData.setLat(135302980L);
		gpsData.setLng(457138797L);
		gpsData.setRpm(30);
		gpsData.setMileage((int) 100);
		gpsData.setFuelWear(1000);
		gpsData.setGpsTime(20170602231700L);   
		gpsData.setIsHistory(0); 
		gpsData.setIsLocated(3);
		gpsData.setCreateDate( date );
		
		gpsDataDynamoDdbDao.addToBatchList(gpsData);
		
		gpsData = new GpsData();
		gpsData.setId(2L);
		gpsData.setDevId("testDevId1234");
		gpsData.setAcconTime(20170602231700L);
		gpsData.setTmTime(20190912022317L);
		gpsData.setSpeed(31);
		gpsData.setDirection((short)5);
		gpsData.setLat(135302980L);
		gpsData.setLng(457138797L);
		gpsData.setRpm(30);
		gpsData.setMileage((int) 100);
		gpsData.setFuelWear(2000);
		gpsData.setGpsTime(20170602231800L);   
		gpsData.setIsHistory(1); 
		gpsData.setIsLocated(3); 
		gpsData.setCreateDate( date );
		gpsDataList.add(gpsData);
		
		gpsDataDynamoDdbDao.addToBatchList(gpsData);
		
		gpsData = new GpsData();
		gpsData.setId(2L);
		gpsData.setDevId("testDevId1234");
		gpsData.setAcconTime(20170602231700L);
		gpsData.setTmTime(20190912022317L);
		gpsData.setSpeed(31);
		gpsData.setDirection((short)5);
		gpsData.setLat(135302980L);
		gpsData.setLng(457138797L);
		gpsData.setRpm(30);
		gpsData.setMileage((int) 100);
		gpsData.setFuelWear(2000);
		gpsData.setGpsTime(20170602231800L);   
		gpsData.setIsHistory(1); 
		gpsData.setIsLocated(3); 
		gpsData.setCreateDate( date );
		gpsDataList.add(gpsData);
		
		gpsDataDynamoDdbDao.addToBatchList(gpsData);
		
		gpsDataDynamoDdbDao.batchInsertWithRetry();
		//fail("Not yet implemented");
	}
	
	/**
	 * - Load test - try to feed over the WCU.
	 */
	@Test
	@Ignore
	//TODO use mocks
	public void batchSaveStressTest() {
		int size = 1_000_000;
		
		SimpleDateFormat createsdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
		createsdf.setTimeZone(TimeZone.getTimeZone("GMT"));
		
		Date date = new Date(System.currentTimeMillis());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		long acconTime = Long.parseLong(sdf.format(date));
		long tmTime = acconTime;
		
		long lat = 135302980L;
		long lng = 457138797L;
		
		int mileage = 100;
		int fuelWear = 2000;
		
		for(int i = 0; i < size; i++) {
			GpsData gpsData = new GpsData();
			gpsData.setId(2L);
			gpsData.setDevId("testDevId1234");
			gpsData.setAcconTime(acconTime);
			gpsData.setTmTime(tmTime++);
			gpsData.setSpeed((int) (Math.random() * 120) + 1);
			gpsData.setDirection((short) ((Math.random() * 360) + 1));
			gpsData.setLat(lat++);
			gpsData.setLng(lng--);
			gpsData.setRpm((int) (Math.random() * 5000) + 1);
			gpsData.setMileage(mileage);
			mileage += 100;
			gpsData.setFuelWear(fuelWear);
			fuelWear += 50;
			gpsData.setGpsTime(tmTime - 1);   
			gpsData.setIsHistory(1); 
			gpsData.setIsLocated(3); 
			gpsData.setCreateDate(new Date());
			
			gpsDataDynamoDdbDao.addToBatchList(gpsData);
		}
		
		gpsDataDynamoDdbDao.batchInsertWithRetry();
		//fail("Not yet implemented");
	}
	
	@Test
	public void testLogFailedBatch() {
		FailedBatch batch1 = new FailedBatch();
		batch1.setException(new ResourceNotFoundException("ResourceNotFoundException"));
		
		Map<String, List<WriteRequest>> unprocessedItems1 = Stream.of(
				new AbstractMap.SimpleEntry<>("testTable", Arrays.asList(new WriteRequest())))
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		batch1.setUnprocessedItems(unprocessedItems1);
		
		FailedBatch batch2 = new FailedBatch();
		
		Map<String, List<WriteRequest>> unprocessedItems2 = Stream.of(
				new AbstractMap.SimpleEntry<>("testTable2", Arrays.asList(new WriteRequest())))
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		batch2.setUnprocessedItems(unprocessedItems2);
		
		FailedBatch batch3 = new FailedBatch();
		batch3.setException(null);
		
		Map<String, List<WriteRequest>> unprocessedItems3 = Stream.of(
				new AbstractMap.SimpleEntry<>("testTable3", Arrays.asList(new WriteRequest())))
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		batch2.setUnprocessedItems(unprocessedItems3);
		
		FailedBatch batch4 = new FailedBatch();
		batch4.setException(null);
		
		Map<String, List<WriteRequest>> unprocessedItems4 = new HashMap<>();
		unprocessedItems4.put(null, null);
		batch4.setUnprocessedItems(unprocessedItems4);
		
		invokeLogFailedBatch(Arrays.asList(batch1, batch2, batch3, batch4));
		invokeLogFailedBatch(null);
		
		List<FailedBatch> failedBatchList = new ArrayList<>();
		failedBatchList.add(null);
		invokeLogFailedBatch(failedBatchList);
	}
	
	/**
	 * Invoke private method logFailedBatch
	 * @param failedBatchList
	 */
	private void invokeLogFailedBatch(List<FailedBatch> failedBatchList) {
		try {
			Method method = GpsDataDynamoDdbDao.class.getDeclaredMethod("logFailedBatch", List.class);
			method.setAccessible(true);
			MyAssertions.assertDoesNotThrow(() -> method.invoke(gpsDataDynamoDdbDao, failedBatchList));
		} catch (NoSuchMethodException | SecurityException | IllegalArgumentException e) {
			e.printStackTrace();
		}
	}
}
