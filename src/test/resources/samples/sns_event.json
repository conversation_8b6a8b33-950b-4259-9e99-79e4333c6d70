{"Records": [{"EventVersion": "1.0", "EventSubscriptionArn": "", "EventSource": "aws:sns", "Sns": {"SignatureVersion": "1", "Signature": "EXAMPLE", "SigningCertUrl": "EXAMPLE", "MessageId": "95df01b4-ee98-5cb9-9903-4c221d41eb5e", "Message": "Hello from SNS!", "MessageAttributes": {"Test": {"Type": "String", "Value": "TestString"}, "TestBinary": {"Type": "Binary", "Value": "TestBinary"}}, "Type": "Notification", "UnsubscribeUrl": "EXAMPLE", "TopicArn": "", "Subject": "TestInvoke"}}]}