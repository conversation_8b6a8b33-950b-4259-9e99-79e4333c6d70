{"Records": [{"kinesis": {"partitionKey": "partitionKey-3", "kinesisSchemaVersion": "1.0", "data": "", "sequenceNumber": "49545115243490985018280067714973144582180062593244200961"}, "eventSource": "aws:kinesis", "eventID": "shardId-************:49545115243490985018280067714973144582180062593244200961", "invokeIdentityArn": "arn:aws:iam::account-id:role/testLEBRole", "eventVersion": "1.0", "eventName": "aws:kinesis:record", "eventSourceARN": "arn:aws:kinesis:us-west-2:35667example:stream/examplestream", "awsRegion": "us-west-2"}]}