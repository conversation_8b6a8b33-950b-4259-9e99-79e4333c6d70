<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" debug="false">

	<property name="GW_LOG_HOME" value="logs" />

	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="stdout" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${GW_LOG_HOME}/stdout.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>${GW_LOG_HOME}/stdout.log.%i</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>10</maxIndex>
		</rollingPolicy>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>20MB</maxFileSize>
		</triggeringPolicy>
		<encoder>
			<pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
		</encoder>
	</appender>

	<root level="${rootLogLevel:-DEBUG}">
		<appender-ref ref="console" />
		<appender-ref ref="stdout" />
	</root>
</configuration>
