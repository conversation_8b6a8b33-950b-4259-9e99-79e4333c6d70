<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.amazonaws.services.lambda.runtime.log4j2.LambdaAppender">
	<Appenders>
		<Lambda name="Lambda">
			<PatternLayout>
				<pattern>%-5p %c{1}:%L - %m%n</pattern>
			</PatternLayout>
		</Lambda>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %C - %msg%n" />
		</Console>
	</Appenders>

	<Loggers>

		<Root level="${env:LOG_LEVEL:-info}">
			<AppenderRef ref="Lambda" />
		</Root>

		<Logger name="io.lettuce" level="WARN" additivity="false"></Logger>
		<Logger name="io.netty" level="WARN" additivity="false"></Logger>
		<Logger name="software.amazon" level="WARN" additivity="false"></Logger>
	</Loggers>
</Configuration>