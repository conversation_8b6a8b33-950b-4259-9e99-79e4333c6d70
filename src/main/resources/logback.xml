<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" debug="false">

    <property name="GW_LOG_HOME" value="logs/vl502" />

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/stdout.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/stdout.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="codec" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/codec.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/codec.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="rawdata" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/rawdata.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/rawdata.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="event" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/event.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/event.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="stat" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/stat.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/stat.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="remote" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/remote.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/remote.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="http" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/http.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/http.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="sql" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/sql.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/error.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="kinesis" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GW_LOG_HOME}/kinesis.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${GW_LOG_HOME}/kinesis.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}.%method:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="io.netty" level="ERROR" />
    <logger name="io.lettuce" level="ERROR" />
    <logger name="com.aws" level="ERROR" />
    <logger name="software.amazon" level="ERROR" />
    <logger name="com.fleetup.gateway.server" level="DEBUG" />
    <logger name="com.amazonaws" level="WARN" />

    <logger name="com.fleetup.gateway.codec" level="${codecLogLevel:-DEBUG}">
        <appender-ref ref="codec"/>
    </logger>

    <logger name="com.fleetup.gateway.logging" level="${eventLogLevel:-WARN}">
        <appender-ref ref="event"/>
    </logger>
    <logger name="stat" level="${statLogLevel:-DEBUG}">
        <appender-ref ref="stat"/>
    </logger>
    <logger name="error" level="${errorLogLevel:-DEBUG}">
        <appender-ref ref="error"/>
    </logger>
    <logger name="remote" level="${statLogLevel:-DEBUG}">
        <appender-ref ref="remote"/>
    </logger>
    <logger name="http" level="${errorLogLevel:-DEBUG}">
        <appender-ref ref="http"/>
    </logger>

    <root level="${rootLogLevel:-DEBUG}">
        <appender-ref ref="stdout" />
        <appender-ref ref="console" />
    </root>
</configuration>
