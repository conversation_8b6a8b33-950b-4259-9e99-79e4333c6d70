# [Server]
# Server Port, optional, numeric
gateway.server.port=30700

# Trip stop time (in seconds), optional, numeric, default value is 600.
gateway.server.idleTime=180
gateway.server.heartBeat=180

# [Logging]
gateway.pub.logging.enable=true
gateway.pub.stat.enable=true

# Root log level, string, optional, default value is INFO.
gateway.server.logLevel=DEBUG


http.service.enable=true
http.service.port=8080
http.cache.endpoint=redis://fleetup-redis-test.8ip2ft.ng.0001.usw2.cache.amazonaws.com:6379/5

#[AWS Kinesis]
# If kinesis plugin is enabled, optional, default value is true
gateway.pub.kinesis.enable=false
# kinesis stream name, if kinesis plugin was enable, need to provide kinesis stream name
gateway.pub.kinesis.stream.name=stress-gw-3vl-stream
# every ${gateway.pub.kinesis.check.inteval}, gw will push event to kinesis, unit millseconds
gateway.pub.kinesis.check.interval=10000
# gateway will use batch push, means ${gateway.pub.kinesis.batch.data.size} records will push every time
gateway.pub.kinesis.batch.data.size=100
# If push data fail, should retry ${gateway.pub.kinesis.retry.times} times, optional, default value is 3
gateway.pub.kinesis.retry.times=3