package com.amazonaws.kinesis.model;


/**
 * @className: GPSRecord
 * @author: Justin
 * @Date: 2024-06-05
 **/
public class GPSRecord {

    private Long id;
    private String devId;
    private Long acconTime;
    private Long tmTime;
    private Integer speed;
    private Integer direction;
    private Long lat;
    private Long lng;
    private Integer rpm;
    private Long mileage;
    private Integer fuel;
    private Long gpsTime;
    private Integer isHistory;
    private Integer locationStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDevId() {
        return devId;
    }

    public void setDevId(String devId) {
        this.devId = devId == null ? null : devId.trim();
    }

    public Long getAcconTime() {
        return acconTime;
    }

    public void setAcconTime(Long acconTime) {
        this.acconTime = acconTime;
    }

    public Long getTmTime() {
        return tmTime;
    }

    public void setTmTime(Long tmTime) {
        this.tmTime = tmTime;
    }

    public Integer getSpeed() {
        return speed;
    }

    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Long getLat() {
        return lat;
    }

    public void setLat(Long lat) {
        this.lat = lat;
    }

    public Long getLng() {
        return lng;
    }

    public void setLng(Long lng) {
        this.lng = lng;
    }

    public Integer getRpm() {
        return rpm;
    }

    public void setRpm(Integer rpm) {
        this.rpm = rpm;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public Integer getFuel() {
        return fuel;
    }

    public void setFuel(Integer fuel) {
        this.fuel = fuel;
    }

    public Long getGpsTime() {
        return gpsTime;
    }

    public void setGpsTime(Long gpsTime) {
        this.gpsTime = gpsTime;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public Integer getLocationStatus() {
        return locationStatus;
    }

    public void setLocationStatus(Integer locationStatus) {
        this.locationStatus = locationStatus;
    }

}
