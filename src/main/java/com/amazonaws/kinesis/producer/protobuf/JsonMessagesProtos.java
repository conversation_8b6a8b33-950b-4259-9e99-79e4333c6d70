// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: json_messages.proto

package com.amazonaws.kinesis.producer.protobuf;

public final class JsonMessagesProtos {
  private JsonMessagesProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface JsonMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:aws.kinesis.protobuf.JsonMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required string jsonBody = 1;</code>
     */
    boolean hasJsonBody();
    /**
     * <code>required string jsonBody = 1;</code>
     */
    java.lang.String getJsonBody();
    /**
     * <code>required string jsonBody = 1;</code>
     */
    com.google.protobuf.ByteString
        getJsonBodyBytes();
  }
  /**
   * Protobuf type {@code aws.kinesis.protobuf.JsonMessage}
   */
  public static final class JsonMessage extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:aws.kinesis.protobuf.JsonMessage)
      JsonMessageOrBuilder {
    // Use JsonMessage.newBuilder() to construct.
    private JsonMessage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private JsonMessage(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final JsonMessage defaultInstance;
    public static JsonMessage getDefaultInstance() {
      return defaultInstance;
    }

    public JsonMessage getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private JsonMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              jsonBody_ = bs;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessage_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.class, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder.class);
    }

    public static com.google.protobuf.Parser<JsonMessage> PARSER =
        new com.google.protobuf.AbstractParser<JsonMessage>() {
      public JsonMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JsonMessage(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<JsonMessage> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int JSONBODY_FIELD_NUMBER = 1;
    private java.lang.Object jsonBody_;
    /**
     * <code>required string jsonBody = 1;</code>
     */
    public boolean hasJsonBody() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string jsonBody = 1;</code>
     */
    public java.lang.String getJsonBody() {
      java.lang.Object ref = jsonBody_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          jsonBody_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string jsonBody = 1;</code>
     */
    public com.google.protobuf.ByteString
        getJsonBodyBytes() {
      java.lang.Object ref = jsonBody_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        jsonBody_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      jsonBody_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasJsonBody()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getJsonBodyBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getJsonBodyBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code aws.kinesis.protobuf.JsonMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:aws.kinesis.protobuf.JsonMessage)
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessage_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.class, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder.class);
      }

      // Construct using com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        jsonBody_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessage_descriptor;
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage getDefaultInstanceForType() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.getDefaultInstance();
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage build() {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage buildPartial() {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage result = new com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.jsonBody_ = jsonBody_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage) {
          return mergeFrom((com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage other) {
        if (other == com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.getDefaultInstance()) return this;
        if (other.hasJsonBody()) {
          bitField0_ |= 0x00000001;
          jsonBody_ = other.jsonBody_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasJsonBody()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object jsonBody_ = "";
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public boolean hasJsonBody() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public java.lang.String getJsonBody() {
        java.lang.Object ref = jsonBody_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            jsonBody_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public com.google.protobuf.ByteString
          getJsonBodyBytes() {
        java.lang.Object ref = jsonBody_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          jsonBody_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public Builder setJsonBody(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        jsonBody_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public Builder clearJsonBody() {
        bitField0_ = (bitField0_ & ~0x00000001);
        jsonBody_ = getDefaultInstance().getJsonBody();
        onChanged();
        return this;
      }
      /**
       * <code>required string jsonBody = 1;</code>
       */
      public Builder setJsonBodyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        jsonBody_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:aws.kinesis.protobuf.JsonMessage)
    }

    static {
      defaultInstance = new JsonMessage(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:aws.kinesis.protobuf.JsonMessage)
  }

  public interface JsonMessagesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:aws.kinesis.protobuf.JsonMessages)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> 
        getJsonMessageList();
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage getJsonMessage(int index);
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    int getJsonMessageCount();
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    java.util.List<? extends com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder> 
        getJsonMessageOrBuilderList();
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder getJsonMessageOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code aws.kinesis.protobuf.JsonMessages}
   */
  public static final class JsonMessages extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:aws.kinesis.protobuf.JsonMessages)
      JsonMessagesOrBuilder {
    // Use JsonMessages.newBuilder() to construct.
    private JsonMessages(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private JsonMessages(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final JsonMessages defaultInstance;
    public static JsonMessages getDefaultInstance() {
      return defaultInstance;
    }

    public JsonMessages getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private JsonMessages(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                jsonMessage_ = new java.util.ArrayList<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage>();
                mutable_bitField0_ |= 0x00000001;
              }
              jsonMessage_.add(input.readMessage(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          jsonMessage_ = java.util.Collections.unmodifiableList(jsonMessage_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessages_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessages_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.class, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.Builder.class);
    }

    public static com.google.protobuf.Parser<JsonMessages> PARSER =
        new com.google.protobuf.AbstractParser<JsonMessages>() {
      public JsonMessages parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JsonMessages(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<JsonMessages> getParserForType() {
      return PARSER;
    }

    public static final int JSONMESSAGE_FIELD_NUMBER = 1;
    private java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> jsonMessage_;
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    public java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> getJsonMessageList() {
      return jsonMessage_;
    }
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    public java.util.List<? extends com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder> 
        getJsonMessageOrBuilderList() {
      return jsonMessage_;
    }
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    public int getJsonMessageCount() {
      return jsonMessage_.size();
    }
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage getJsonMessage(int index) {
      return jsonMessage_.get(index);
    }
    /**
     * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
     */
    public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder getJsonMessageOrBuilder(
        int index) {
      return jsonMessage_.get(index);
    }

    private void initFields() {
      jsonMessage_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      for (int i = 0; i < getJsonMessageCount(); i++) {
        if (!getJsonMessage(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < jsonMessage_.size(); i++) {
        output.writeMessage(1, jsonMessage_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < jsonMessage_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, jsonMessage_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code aws.kinesis.protobuf.JsonMessages}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:aws.kinesis.protobuf.JsonMessages)
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessagesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessages_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessages_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.class, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.Builder.class);
      }

      // Construct using com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getJsonMessageFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (jsonMessageBuilder_ == null) {
          jsonMessage_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          jsonMessageBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.internal_static_aws_kinesis_protobuf_JsonMessages_descriptor;
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages getDefaultInstanceForType() {
        return com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.getDefaultInstance();
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages build() {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages buildPartial() {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages result = new com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages(this);
        int from_bitField0_ = bitField0_;
        if (jsonMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            jsonMessage_ = java.util.Collections.unmodifiableList(jsonMessage_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.jsonMessage_ = jsonMessage_;
        } else {
          result.jsonMessage_ = jsonMessageBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages) {
          return mergeFrom((com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages other) {
        if (other == com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages.getDefaultInstance()) return this;
        if (jsonMessageBuilder_ == null) {
          if (!other.jsonMessage_.isEmpty()) {
            if (jsonMessage_.isEmpty()) {
              jsonMessage_ = other.jsonMessage_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureJsonMessageIsMutable();
              jsonMessage_.addAll(other.jsonMessage_);
            }
            onChanged();
          }
        } else {
          if (!other.jsonMessage_.isEmpty()) {
            if (jsonMessageBuilder_.isEmpty()) {
              jsonMessageBuilder_.dispose();
              jsonMessageBuilder_ = null;
              jsonMessage_ = other.jsonMessage_;
              bitField0_ = (bitField0_ & ~0x00000001);
              jsonMessageBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getJsonMessageFieldBuilder() : null;
            } else {
              jsonMessageBuilder_.addAllMessages(other.jsonMessage_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getJsonMessageCount(); i++) {
          if (!getJsonMessage(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessages) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> jsonMessage_ =
        java.util.Collections.emptyList();
      private void ensureJsonMessageIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          jsonMessage_ = new java.util.ArrayList<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage>(jsonMessage_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder> jsonMessageBuilder_;

      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> getJsonMessageList() {
        if (jsonMessageBuilder_ == null) {
          return java.util.Collections.unmodifiableList(jsonMessage_);
        } else {
          return jsonMessageBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public int getJsonMessageCount() {
        if (jsonMessageBuilder_ == null) {
          return jsonMessage_.size();
        } else {
          return jsonMessageBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage getJsonMessage(int index) {
        if (jsonMessageBuilder_ == null) {
          return jsonMessage_.get(index);
        } else {
          return jsonMessageBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder setJsonMessage(
          int index, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage value) {
        if (jsonMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureJsonMessageIsMutable();
          jsonMessage_.set(index, value);
          onChanged();
        } else {
          jsonMessageBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder setJsonMessage(
          int index, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder builderForValue) {
        if (jsonMessageBuilder_ == null) {
          ensureJsonMessageIsMutable();
          jsonMessage_.set(index, builderForValue.build());
          onChanged();
        } else {
          jsonMessageBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder addJsonMessage(com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage value) {
        if (jsonMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureJsonMessageIsMutable();
          jsonMessage_.add(value);
          onChanged();
        } else {
          jsonMessageBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder addJsonMessage(
          int index, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage value) {
        if (jsonMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureJsonMessageIsMutable();
          jsonMessage_.add(index, value);
          onChanged();
        } else {
          jsonMessageBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder addJsonMessage(
          com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder builderForValue) {
        if (jsonMessageBuilder_ == null) {
          ensureJsonMessageIsMutable();
          jsonMessage_.add(builderForValue.build());
          onChanged();
        } else {
          jsonMessageBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder addJsonMessage(
          int index, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder builderForValue) {
        if (jsonMessageBuilder_ == null) {
          ensureJsonMessageIsMutable();
          jsonMessage_.add(index, builderForValue.build());
          onChanged();
        } else {
          jsonMessageBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder addAllJsonMessage(
          java.lang.Iterable<? extends com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage> values) {
        if (jsonMessageBuilder_ == null) {
          ensureJsonMessageIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, jsonMessage_);
          onChanged();
        } else {
          jsonMessageBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder clearJsonMessage() {
        if (jsonMessageBuilder_ == null) {
          jsonMessage_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          jsonMessageBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public Builder removeJsonMessage(int index) {
        if (jsonMessageBuilder_ == null) {
          ensureJsonMessageIsMutable();
          jsonMessage_.remove(index);
          onChanged();
        } else {
          jsonMessageBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder getJsonMessageBuilder(
          int index) {
        return getJsonMessageFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder getJsonMessageOrBuilder(
          int index) {
        if (jsonMessageBuilder_ == null) {
          return jsonMessage_.get(index);  } else {
          return jsonMessageBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public java.util.List<? extends com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder> 
           getJsonMessageOrBuilderList() {
        if (jsonMessageBuilder_ != null) {
          return jsonMessageBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(jsonMessage_);
        }
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder addJsonMessageBuilder() {
        return getJsonMessageFieldBuilder().addBuilder(
            com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder addJsonMessageBuilder(
          int index) {
        return getJsonMessageFieldBuilder().addBuilder(
            index, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .aws.kinesis.protobuf.JsonMessage jsonMessage = 1;</code>
       */
      public java.util.List<com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder> 
           getJsonMessageBuilderList() {
        return getJsonMessageFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder> 
          getJsonMessageFieldBuilder() {
        if (jsonMessageBuilder_ == null) {
          jsonMessageBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessage.Builder, com.amazonaws.kinesis.producer.protobuf.JsonMessagesProtos.JsonMessageOrBuilder>(
                  jsonMessage_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          jsonMessage_ = null;
        }
        return jsonMessageBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:aws.kinesis.protobuf.JsonMessages)
    }

    static {
      defaultInstance = new JsonMessages(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:aws.kinesis.protobuf.JsonMessages)
  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_aws_kinesis_protobuf_JsonMessage_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_aws_kinesis_protobuf_JsonMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_aws_kinesis_protobuf_JsonMessages_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_aws_kinesis_protobuf_JsonMessages_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023json_messages.proto\022\024aws.kinesis.proto" +
      "buf\"\037\n\013JsonMessage\022\020\n\010jsonBody\030\001 \002(\t\"F\n\014" +
      "JsonMessages\0226\n\013jsonMessage\030\001 \003(\0132!.aws." +
      "kinesis.protobuf.JsonMessageB=\n\'com.amaz" +
      "onaws.kinesis.producer.protobufB\022JsonMes" +
      "sagesProtos"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_aws_kinesis_protobuf_JsonMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_aws_kinesis_protobuf_JsonMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_aws_kinesis_protobuf_JsonMessage_descriptor,
        new java.lang.String[] { "JsonBody", });
    internal_static_aws_kinesis_protobuf_JsonMessages_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_aws_kinesis_protobuf_JsonMessages_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_aws_kinesis_protobuf_JsonMessages_descriptor,
        new java.lang.String[] { "JsonMessage", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
