package com.fleetup.gateway.logging;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.util.RawdataUtil;
import com.google.common.io.Files;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @className: MultiThreadsHexDumpFilter
 * @author: Justin
 * @Date: 2024-05-17
 **/
@ChannelHandler.Sharable
public class MultiThreadsHexDumpFilter extends ChannelDuplexHandler {

    private static final Logger logger = LoggerFactory.getLogger(MultiThreadsHexDumpFilter.class);
    private static final Logger statLogger = LoggerFactory.getLogger("stat");
    private static final String READ_FLAG = "<<";
    private static final String WRITE_FLAG = ">>";
    private static final String DEFAULT_RAW_DATA_FOLDER = "rawdata/vl502";
    private static final String CRLF = "\n";
    private static final String PATH_SEPARATOR = "/";
    private final SimpleDateFormat defaultTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss.SSS");
    private boolean loggingOnDump;

    int queueGroupSize = 10;
    List<Queue<String[]>> rawdataQueueGroup = new ArrayList<>();

    /**
     * Default Constructor.
     */
    public MultiThreadsHexDumpFilter() {
        this(true);
    }

    /**
     *
     * Only print log when loggingOnDump is true and logging level under DEBUG
     *
     * @param loggingOnDump
     */
    public MultiThreadsHexDumpFilter(boolean loggingOnDump) {
        this(DEFAULT_RAW_DATA_FOLDER, loggingOnDump);
    }

    public MultiThreadsHexDumpFilter(String rawDataPath) {
        this(rawDataPath, true);
    }

    static class FileWriteTask implements Runnable{
        Queue<String[]> queue;
        volatile boolean isRunning = true;
        public FileWriteTask(Queue<String[]> queue) {
            this.queue = queue;
        }

        @Override
        public void run() {
            while(isRunning) {
                if (queue.isEmpty()) {
                    statLogger.warn("rawdataQueue is empty");
                    try {
                        TimeUnit.MILLISECONDS.sleep(1000);
                    } catch (InterruptedException e) {
                        logger.warn("Write rawdata failed, ", e);
                        Thread.currentThread().interrupt();
                    }
                    continue;
                }
                String[] rawdata = queue.poll();
                try {
                    Files.append(rawdata[1], getFile(rawdata[0]), StandardCharsets.UTF_8);
                } catch (IOException e) {
                    statLogger.warn("Write rawdata fail, ", e);
                }

                int leftSize = queue.size();
                if (leftSize > 1000 && leftSize % 1000 == 0) {
                    statLogger.warn("{} lines of rawdata is pending for writting", leftSize);
                }
            }
        }

        private File getFile(String devId){
            String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
            File rawDataSubFolder = new File(DEFAULT_RAW_DATA_FOLDER+PATH_SEPARATOR+date);
            if (!rawDataSubFolder.exists()) {
                rawDataSubFolder.mkdirs();
            }
            return new File(DEFAULT_RAW_DATA_FOLDER + PATH_SEPARATOR + date + PATH_SEPARATOR +devId + "_" + date +".txt");
        }

    }

    public MultiThreadsHexDumpFilter(String rawDataPath, boolean loggingOnDump) {
        this.loggingOnDump = loggingOnDump;
        if (loggingOnDump) {
            File rawDataFolder = new File(rawDataPath);
            if (!rawDataFolder.exists()) {
                rawDataFolder.mkdirs();
            }

            for (int i = 0; i < queueGroupSize; i++) {
                rawdataQueueGroup.add(new ConcurrentLinkedQueue<>());
            }

            ExecutorService service = Executors.newFixedThreadPool(queueGroupSize+1);
            rawdataQueueGroup.forEach(q -> service.execute(new FileWriteTask(q)));
        }
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.info("write: {}, {}", msg, (msg instanceof BaseStructure));
        }
        if (loggingOnDump) {
            this.dumpHex(ctx, msg, WRITE_FLAG);
        }
        ctx.write(msg, promise);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object message) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.info("read:{}", message);
        }
        if (loggingOnDump) {
            this.dumpHex(ctx, message, READ_FLAG);
        }
        ctx.fireChannelRead(message);
    }

    private void dumpHex(ChannelHandlerContext ctx, Object message, String direction){
        StringBuilder sb = new StringBuilder(defaultTimeFormat.format(new java.util.Date()) + " " + ctx.channel().id()+direction);
        String devId = (String)ctx.channel().attr(AttributeKey.valueOf("DEVICE_ID")).get();
        logger.debug("devId = {}", devId);

        String content = RawdataUtil.formatMessage(ctx, direction, message);
        sb.append(content);

        if (loggingOnDump) {
            logger.debug("HexDump:{}", sb);
        }

        if (devId != null) {
            sb.append(CRLF);

            rawdataQueueGroup.get(Math.abs(devId.hashCode()%queueGroupSize)).add(new String[]{devId,sb.toString()});
        } else {
            sb.append(CRLF);
            try {
                Files.append(sb.toString(), RawdataUtil.getFile(DEFAULT_RAW_DATA_FOLDER), StandardCharsets.UTF_8);
            } catch (IOException e) {
                logger.warn("Write rawdata fail, ", e);
            }
        }
    }


}

