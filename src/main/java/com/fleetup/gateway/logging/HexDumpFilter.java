package com.fleetup.gateway.logging;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.RawdataUtil;
import com.google.common.io.Files;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

/**
 * @className: HexDumpFilter
 * @author: Justin
 * @Date: 2024-05-16
 **/
@ChannelHandler.Sharable
public class HexDumpFilter extends ChannelDuplexHandler {
    private static final Logger logger = LoggerFactory.getLogger(HexDumpFilter.class);
    private static final String READ_FLAG = "<<";
    private static final String WRITE_FLAG = ">>";
    private static final String DEFAULT_RAW_DATA_FOLDER = "rawdata/vl502";
    private static final String CRLF = "\n";
    private final SimpleDateFormat defaultFormat = new SimpleDateFormat("yyyyMMddHHmmss.SSS");
    private final boolean loggingOnDump;

    /**
     * Default Constructor.
     */
    public HexDumpFilter() {
        this(true);
    }

    /**
     *
     * Only print log when loggingOnDump is true and logging level under DEBUG
     *
     * @param loggingOnDump
     */
    public HexDumpFilter(boolean loggingOnDump) {
        this(DEFAULT_RAW_DATA_FOLDER, loggingOnDump);
    }

    public HexDumpFilter(String rawDataPath) {
        this(rawDataPath, true);
    }


    public HexDumpFilter(String rawDataPath, boolean loggingOnDump) {
        this.loggingOnDump = loggingOnDump;
        if (loggingOnDump) {
            File rawDataFolder = new File(rawDataPath);
            if (!rawDataFolder.exists()) {
                rawDataFolder.mkdirs();
            }
        }
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.info("write: {}, {}", msg, (msg instanceof BaseStructure));
        }
        if (loggingOnDump) {
            this.dumpHex(ctx, msg, WRITE_FLAG);
        }
        ctx.write(msg, promise);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object message) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.info("read: {}", message);
        }
        if (loggingOnDump) {
            this.dumpHex(ctx, message, READ_FLAG);
        }
        ctx.fireChannelRead(message);
    }

    private void dumpHex(ChannelHandlerContext ctx, Object message, String direction){
        StringBuilder sb = new StringBuilder(defaultFormat.format(new java.util.Date()) + " " + ctx.channel().id()+direction);
        String devId = (String)ctx.channel().attr(AttributeKey.valueOf("DEVICE_ID")).get();
        devId = Hex.getAllDevId(devId);
        logger.debug("devId = {}", devId);
        String stringContent = RawdataUtil.formatMessage(ctx, direction, message);
        sb.append(stringContent);

        if (loggingOnDump) {
            logger.debug("HexDump:{}", sb);
        }

        if (devId != null) {
            sb.append(CRLF);
            try {
                Files.append(sb, RawdataUtil.getFile(devId, DEFAULT_RAW_DATA_FOLDER), StandardCharsets.UTF_8);
            } catch (IOException e) {
                logger.error("Write message[{}] to file[{}] failed", sb, RawdataUtil.getFile(devId, DEFAULT_RAW_DATA_FOLDER).getAbsolutePath(), e);
            }

        } else {
            sb.append(CRLF);
            try {
                Files.append(sb.toString(), RawdataUtil.getFile(DEFAULT_RAW_DATA_FOLDER), StandardCharsets.UTF_8);
            } catch (IOException e) {
                logger.error("Write message[{}] to file[{}] failed", sb, RawdataUtil.getFile(DEFAULT_RAW_DATA_FOLDER).getAbsolutePath(), e);
            }
        }
    }
}
