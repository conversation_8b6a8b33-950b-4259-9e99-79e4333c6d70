package com.fleetup.gateway.logging;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.util.RawdataUtil;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @className: HexLoggingFilter
 * @author: Justin
 * @Date: 2024-05-17
 **/
@ChannelHandler.Sharable
public class HexLoggingFilter extends ChannelDuplexHandler {
    private static final Logger logger = LoggerFactory.getLogger(HexLoggingFilter.class);
    private static final String READ_FLAG = "<<";
    private static final String WRITE_FLAG = ">>";
    private static final String CRLF = "\n";

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.info("write:{}, {}", msg, (msg instanceof BaseStructure));
        }
        this.dumpHex(ctx, msg, WRITE_FLAG);
        ctx.write(msg, promise);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object message) throws Exception {
        this.dumpHex(ctx, message, READ_FLAG);
        ctx.fireChannelRead(message);
    }

    private void dumpHex(ChannelHandlerContext ctx, Object message, String direction){
        StringBuilder sb = new StringBuilder(ctx.channel().id()+direction);

        String content = RawdataUtil.formatMessage(ctx, direction, message);
        sb.append(content);

        sb.append(CRLF);
        logger.info("{}:{}", ctx.channel().attr(Constants.ATTR_KEY_DEV_ID).get(), sb);
    }
}

