package com.fleetup.gateway.server;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.security.CodeSource;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;

/**
 * @className: Version
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class Version {
    private static final Logger LOG = LoggerFactory.getLogger(Version.class);
    private static final int LENGTH_OF_JAR_EXTENSION = 4;
    private static final String DEFAULT_VERSION = "-unknown";

    private Version(){
    }
    static {
        // check if exist duplicate jars
        Version.checkDuplicate(Version.class);
    }

    public static String getVersion(){
        return getVersion(Version.class, DEFAULT_VERSION);
    }

    public static String getVersion(Class<?> cls, String defaultVersion) {
        try {
            // First, check version number in MANIFEST.MF(Java Specification)
            String version = cls.getPackage().getImplementationVersion();
            if (version == null || version.length() == 0) {
                version = cls.getPackage().getSpecificationVersion();
            }
            if (version == null || version.length() == 0) {
                // If can not find version in MANIFEST.MF, try to get version# from package name(jar name)
                CodeSource codeSource = cls.getProtectionDomain().getCodeSource();
                if(codeSource == null) {
                    LOG.info("No codeSource for class " + cls.getName() + " when getVersion, use default version " + defaultVersion);
                } else {
                    String file = codeSource.getLocation().getFile();
                    if (isJarFile(file)) {
                        file = file.substring(0, file.length() - LENGTH_OF_JAR_EXTENSION);
                        int i = file.lastIndexOf('/');
                        if (i >= 0) {
                            file = file.substring(i + 1);
                        }
                        i = file.indexOf("-");
                        if (i >= 0) {
                            file = file.substring(i + 1);
                        }
                        while (file.length() > 0 && ! Character.isDigit(file.charAt(0))) {
                            i = file.indexOf("-");
                            if (i >= 0) {
                                file = file.substring(i + 1);
                            } else {
                                break;
                            }
                        }
                        version = file;
                    }
                }
            }
            // Still can not version#, return default version#
            return version == null || version.length() == 0 ? defaultVersion : version;
        } catch (Exception e) {
            // If exception occurs, return default version#
            LOG.error("return default version, ignore exception " + e.getMessage(), e);
            return defaultVersion;
        }
    }

    public static void checkDuplicate(Class<?> cls, boolean failOnError) {
        checkDuplicate(cls.getName().replace('.', '/') + ".class", failOnError);
    }

    public static void checkDuplicate(Class<?> cls) {
        checkDuplicate(cls, false);
    }

    public static void checkDuplicate(String path, boolean failOnError) {
        try {
            // Check in ClassPath
            Enumeration<URL> urls = Version.class.getClassLoader().getResources(path);
            Set<String> files = new HashSet<>();
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null && url.getFile() != null) {
                    files.add(url.getFile());
                }
            }
            // If find more than one file, that is duplicate
            if (files.size() > 1) {
                String error = "Duplicate class " + path + " in " + files.size() + " jar " + files;
                if (failOnError) {
                    throw new IllegalStateException(error);
                } else {
                    LOG.error(error);
                }
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    private static boolean isJarFile(String fileName){
        return fileName != null && fileName.length() > 0 && fileName.endsWith(".jar");
    }
}
