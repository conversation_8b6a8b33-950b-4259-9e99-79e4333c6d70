package com.fleetup.gateway.server.vl502;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: GatewayWorker
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class GatewayWorker {
    private static final Logger LOG = LoggerFactory.getLogger(GatewayWorker.class);
    private static final int EXT_THREAD_POOL_SIZE = 6;
    ScheduledExecutorService executor;

    public GatewayWorker(){
        ThreadFactory threadFactory = new ThreadFactory() {
            private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            private static final String THREAD_NAME_PREFIX = "gatewayWorker-";
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = defaultFactory.newThread(r);
                thread.setName(THREAD_NAME_PREFIX + threadNumber.getAndIncrement());
                return thread;
            }
        };
        executor = Executors.newScheduledThreadPool(EXT_THREAD_POOL_SIZE, threadFactory);
    }

    public void register(Runnable thread, int intervalInSeconds){
        LOG.info("Register: thread={}, intervalInSeconds={}", thread, intervalInSeconds);
        executor.scheduleWithFixedDelay(thread, intervalInSeconds, intervalInSeconds, TimeUnit.SECONDS);
    }

    void stop(){
        if (executor != null) {
            executor.shutdown();
        }
    }
}

