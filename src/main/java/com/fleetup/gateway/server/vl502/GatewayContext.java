package com.fleetup.gateway.server.vl502;


import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.IDataPublisher;
import com.fleetup.gateway.plugin.IDataPublisherCallback;
import com.fleetup.gateway.server.IServer;
import com.fleetup.gateway.server.Version;
import com.fleetup.gateway.util.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @className: GatewayContext
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class GatewayContext {
    private static final Logger LOG = LoggerFactory.getLogger(GatewayContext.class);
    private static final Logger CONSOLE_LOG = LoggerFactory.getLogger("console");

    public static final GatewayContext me = new GatewayContext();
    Config config;
    GatewayWorker worker;
    IServer server;

    List<IDataPublisher> dataPublishers = new ArrayList<>();

    private GatewayContext(){
        this.showBanner();
        this.config = new Config();
        setLoggingLevel(config.getLog());
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }

    private void showBanner(){
        int tmp;
        StringBuilder stringBuilder = new StringBuilder("\n");
        try (InputStream in = GatewayContext.class.getResourceAsStream("/banner.txt")){
            while ((tmp = in.read()) > -1) {
                stringBuilder.append((char)tmp);
            }
            stringBuilder.append("Gateway v").append(Version.getVersion());
            String banner = stringBuilder.toString();
            CONSOLE_LOG.info(banner);
        } catch (IOException e) {
            LOG.error("Render banner failed", e);
        }
    }

    void setLoggingLevel(String level) {
        System.out.println(String.format("Change log level: [%s, %s]", level, "ROOT"));
        ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) org.slf4j.LoggerFactory.getLogger("ROOT");
        logger.setLevel(ch.qos.logback.classic.Level.valueOf(level));
    }

    public void run(){
        this.worker = new GatewayWorker();
        this.server = new Server();
        ServiceLoader<IDataPublisher> publisherServices = ServiceLoader.load(IDataPublisher.class);
        Iterator<IDataPublisher> publishers = publisherServices.iterator();

        while (publishers.hasNext()) {
            IDataPublisher publisher = publishers.next();
            if (publisher.start(config.getProperties())) {
                dataPublishers.add(publisher);
                LOG.info("Plugin["+publisher.getClass().getSimpleName()+"] started");
            } else {
                LOG.info("Plugin["+publisher.getClass().getSimpleName()+"] disabled");
            }
        }

        LOG.info("IDataPublisher size={}", dataPublishers.size());
        IDataPublisherCallback callback;
        for (IDataPublisher iDataPublisher : publisherServices) {
            callback = iDataPublisher.getCallback();
            if (callback != null) {
                callback.exec(server);
            }
        }

        Runtime.getRuntime().addShutdownHook(new Thread("ShudownHook"){
            @Override
            public void run() {
                super.run();
                LOG.info("Shutdown gateway...");
                server.shutdown();
                stopAllPublishers();

                LOG.info("Shutdown gateway successfully.");
            }
        });

        this.startServer();
    }

    public Config getConfig(){
        return this.config;
    }

    public GatewayWorker getWorker() {
        return worker;
    }

    public IServer getServer() {
        return server;
    }

    public <T> void publishEvent(EventMessage<T> eventMessage){
        eventMessage.setDevId(Hex.getAllDevId(eventMessage.getDevId()));
        for (IDataPublisher iDataPublisher : dataPublishers) {
            iDataPublisher.publishRecord(eventMessage);
        }
    }

    void startServer(){
        try {
            CONSOLE_LOG.info("Server started at {}", LocalDateTime.now());
            server.bind(config);
        } catch (Exception e) {
            LOG.error("Start gateway server failed", e);
            stopAllPublishers();
            System.exit(-1);
        }
    }

    void stopAllPublishers(){
        for (IDataPublisher iDataPublisher : dataPublishers) {
            iDataPublisher.stop();
        }
    }



}
