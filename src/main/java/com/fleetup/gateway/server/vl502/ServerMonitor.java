package com.fleetup.gateway.server.vl502;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.plugin.Config;
import io.netty.channel.group.ChannelGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @className: ServerMonitor
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class ServerMonitor {
    private static final Logger LOG = LoggerFactory.getLogger(ServerMonitor.class);

    private static final Integer MONITOR_INTERVAL_IN_SECONDS = 60;

    private static final Integer MONITOR_TOTAL_SECONDS = 960;

    private int monitorTotalTime;

    private ChannelGroup channels;

    private ScheduledExecutorService executor;

    public ServerMonitor(ChannelGroup channels, Config config) {
        this.channels = channels;
        monitorTotalTime = config.getHeartBeatTime();
    }

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactory() {
        private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = defaultFactory.newThread(r);
            thread.setName("ServerMonitor-" + threadNumber.getAndIncrement());
            return thread;
        }
    };

    public void run() {
        executor = Executors.newScheduledThreadPool(1, THREAD_FACTORY);
        executor.scheduleWithFixedDelay(new Thread("ServerMonitor") {
            @Override
            public void run() {
                if (channels.size() > 0) {
                    String devices = channels.stream().map(channel -> channel.attr(Constants.ATTR_KEY_DEV_ID).get()).collect(Collectors.joining(","));
                    LOG.info("Total Online Device : {} .devices : {}", channels.size(), devices);
                    monitorTotalTime = MONITOR_TOTAL_SECONDS;
                } else {
                    LOG.info("monitorTotalTime : {} s.", monitorTotalTime);
                    if(monitorTotalTime > 0) {
                        monitorTotalTime = monitorTotalTime - MONITOR_INTERVAL_IN_SECONDS;
                    }
                }
            }
        }, MONITOR_INTERVAL_IN_SECONDS, MONITOR_INTERVAL_IN_SECONDS, TimeUnit.SECONDS);
    }


    public void shutdown() {
        if (executor != null) {
            executor.shutdown();
        }
    }

}

