package com.fleetup.gateway.server.vl502;


import com.fleetup.gateway.codec.MessageCodecFactory;
import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.config.CommandRequest;
import com.fleetup.gateway.logging.HexDumpFilter;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.server.IServer;
import com.fleetup.gateway.server.ServerException;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.MessageSeq;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.GlobalEventExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @className: Server
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class Server implements IServer {
    private static final Logger LOG = LoggerFactory.getLogger(Server.class);
    private ChannelGroup channels = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    final ServerBootstrap serverBootstrap = new ServerBootstrap();
    final HexDumpFilter rawDataDumper = new HexDumpFilter(true);

    EventLoopGroup bossGroup = null;
    EventLoopGroup workerGroup = null;

    public Server() {
        LOG.info("Server initializing...");
    }

    @Override
    public void bind(final Config config) throws ServerException {
        LOG.info("Server binding....");
        bossGroup = new NioEventLoopGroup();
        workerGroup = new NioEventLoopGroup();
        final MessageHandler fleetUPServerHandler = new MessageHandler(channels);
        try {
            serverBootstrap.group(bossGroup, workerGroup).channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5 * 1000)
                    .handler(new LoggingHandler(LogLevel.DEBUG)).childHandler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel ch) {
                    ChannelPipeline pipeline = ch.pipeline();
                    pipeline.addLast("idleStateHandler", new IdleStateHandler(0, 0, config.getIdleTime(), TimeUnit.SECONDS));
                    pipeline.addLast("rawDataDumper", rawDataDumper);
                    pipeline.addLast("fleetUPCodecFactory", new MessageCodecFactory());
                    pipeline.addLast("fleetUPServerHandler", fleetUPServerHandler);
                    if (LOG.isDebugEnabled()) {
                        LOG.debug("pipeline = {}", pipeline);
                    }
                }
            });
            LOG.info("serverBootstrap.config: {}", serverBootstrap.config());
            ChannelFuture channelFuture = serverBootstrap.bind(config.getPort()).sync();
            LOG.info("Server started successfully, running on port: {}", config.getPort());
            channelFuture.channel().closeFuture().sync();
        } catch (Exception e) {
            throw new ServerException("Start server failed", e);
        } finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }

    @Override
    public ServerBootstrap getIoAcceptor() {
        return serverBootstrap;
    }

    @Override
    public ChannelGroup getChannels() {
        return channels;
    }

    @Override
    public void shutdown() {
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();
    }

    @Override
    public void pushMessage(String devId, byte[] payload) {
        LOG.debug("PushMessage: target[{}]", devId);
        for (Channel channel : channels) {
            if (Objects.equals(getDeviceId(channel), devId)) {
                channel.writeAndFlush(Unpooled.wrappedBuffer(payload));
                LOG.info("pushMessage {} - {}", devId, Hex.toHexString(payload));
                return;
            }
        }
        LOG.info("PushMessage: target[{}] not online, ignore the message[{}]", devId, Hex.toHexString(payload));
    }

    @Override
    public void upgrade(String devId, String firmwareVersion, String firmwareLocation) {

    }

    @Override
    public void restoreUpgrade(String devId, String firmwareVersion, String firmwareLocation) {

    }

    @Override
    public int pushCommand(String devId, String command,int seqNum) {
        pushMessage(Hex.revertDevId(devId),new BaseProtocol(Hex.revertDevId(devId), HTConstants.COMMAND_QUERY, new CommandRequest(command), seqNum).getMessage());
        return 0;
    }

    private String getDeviceId(Channel channel){
        return channel.attr(Constants.ATTR_KEY_DEV_ID).get();
    }
}
