package com.fleetup.gateway.server.vl502;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.server.handler.DataHandler;
import com.fleetup.gateway.server.handler.DefaultDataHandler;
import com.fleetup.gateway.server.handler.MessageHandlerMapping;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.MessageSeq;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.group.ChannelGroup;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * @className: MessageHandler
 * @author: Justin
 * @Date: 2024-05-17
 **/
@ChannelHandler.Sharable
public class MessageHandler extends SimpleChannelInboundHandler<BaseStructure> {
    private static final Logger logger = LoggerFactory.getLogger(MessageHandler.class);
    private static Logger errLogger = LoggerFactory.getLogger("error");
    private static Logger accessLogger = LoggerFactory.getLogger("access");

    private final DataHandler defaultHandler = new DefaultDataHandler();
    private Map<Integer, DataHandler> handlers;
    private ChannelGroup channels;
    int counter;

    public MessageHandler(ChannelGroup channels) {
        this.channels = channels;
        handlers = MessageHandlerMapping.asMap();
        logger.info("handlers = {}", handlers);
    }


    @Override
    protected void channelRead0(ChannelHandlerContext ctx, BaseStructure message) throws Exception {
        logger.debug("message[{}]:{}", ++counter, message);
        Channel session = ctx.channel();
        if (message instanceof BaseProtocol) {
            BaseProtocol baseProtocol = (BaseProtocol) message;
            int protocolId = baseProtocol.getMessageId().getValue();
            String deviceId = baseProtocol.getDeviceId().getValue();
            logger.info("devId: {} ", deviceId);
            if (protocolId == HTConstants.CONN_MO_REGISTRATION){
                handlers.get(protocolId).handle(session, deviceId, baseProtocol);
                return;
            }

            if (protocolId == HTConstants.CONN_MO_LOGIN) {
                deviceId = baseProtocol.getDeviceId().getValue();
                logger.debug("devId2: {} ", deviceId);
                for (Channel channel : channels){
                    if (deviceId.equals(getDeviceId(channel)) && !channel.id().equals(ctx.channel().id())){
                        logger.warn("device {} reconnect, need to close old channel : {}", deviceId, channel.id());
                        channel.attr(Constants.ATTR_KEY_DEV_ID).set(null);
                        channel.close();
                    }
                }
                session.attr(Constants.ATTR_KEY_DEV_ID).set(deviceId);
            } else if (deviceId == null || !deviceId.equals(baseProtocol.getDeviceId().getValue())) {
                logger.warn("NoAuth: disconnect [{}]", deviceId);
                session.close();
                return;
            }
            if (handlers.containsKey(protocolId)){
                handlers.getOrDefault(protocolId, defaultHandler).handle(session, deviceId, baseProtocol);
            }else{
                logger.warn("Missing handler for ProtocolId:{} , Hex : {}",baseProtocol.getMessageId().getValue(), Hex.toHexString(baseProtocol.getMessageId().toBytes()));
            }

        }

        ctx.fireChannelRead(message);
    }

    /**
     * pipeline.addLast("testHttpServerHandler", new TestHttpServerHandler());
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        logger.debug("{}", ctx);
        super.handlerAdded(ctx);
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        logger.debug("{}", ctx);
        accessLogger.debug("Registered->{}", ctx.channel());
        super.channelRegistered(ctx);
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        logger.debug("{}", ctx);
        channels.add(ctx.channel());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.debug("{}", ctx);
        super.channelInactive(ctx);
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
        logger.debug("{}", ctx);
        accessLogger.debug("Unregistered->{}", ctx.channel().attr(Constants.ATTR_KEY_DEV_ID).get());
        Channel session = ctx.channel();
        String deviceId = session.attr(Constants.ATTR_KEY_DEV_ID).get();
        if (deviceId != null) {
            MessageSeq.getInstance().resetSeq(deviceId);
            handlers.get(HTConstants.CHNNEL_UNREGISTERED).handle(session, deviceId, null);
        }
        channels.remove(ctx.channel());
        super.channelUnregistered(ctx);
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        logger.debug("evt class: {}", evt.getClass());
        if (IdleStateEvent.class.isAssignableFrom(evt.getClass())) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.ALL_IDLE) {
                logger.warn("[{}]Idling triggered, disconnected by server:{}, event:{}", getDeviceId(ctx.channel()), ctx, event);
                ctx.close();
            }
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (!(cause instanceof IOException && cause.getMessage().contains("Connection reset by peer"))){
            errLogger.error("Error happened in {}", ctx, cause);
        }
        super.exceptionCaught(ctx, cause);
        channels.remove(ctx.channel());
    }

    private String getDeviceId(Channel session) {
        return session.attr(Constants.ATTR_KEY_DEV_ID).get();
    }

}
