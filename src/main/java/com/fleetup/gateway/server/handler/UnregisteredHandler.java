package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.event.LogoutEvent;
import com.fleetup.gateway.server.vl502.GatewayContext;
import io.netty.channel.Channel;

/**
 * @className: UnregisteredHandler
 * @author: Justin
 * @Date: 2024-05-20
 **/
public class UnregisteredHandler extends DefaultDataHandler {

    public UnregisteredHandler() {
        super();
    }


    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_CONN_MO_LOGOUT, deviceId, buildEvent()));
    }

    LogoutEvent buildEvent(){
        LogoutEvent event = new LogoutEvent();
        return event;
    }
}
