package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.PlatformGeneralResponse;
import com.fleetup.gateway.util.Hex;
import io.netty.channel.Channel;


/**
 * @className: DefaultDataHandler
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class DefaultDataHandler extends DataHandler {
    public DefaultDataHandler() {
        super();
    }

    @Override
    public void doResponse(Channel session, String deviceId, BaseProtocol baseProtocol){
        if (baseProtocol != null && baseProtocol.getMessageId() != null){
//            logger.warn("Missing handler for ProtocolId:{} , Hex : {}",baseProtocol.getMessageId().getValue(), Hex.toHexString(baseProtocol.getMessageId().toBytes()));
            session.writeAndFlush(new BaseProtocol(deviceId, HTConstants.REPORTS_MT_GENERAL,new PlatformGeneralResponse(baseProtocol.getSerialNumber().getValue(),baseProtocol.getMessageId().getValue(),0), SEQ.getNextSeq(deviceId)));
        }

    }
}

