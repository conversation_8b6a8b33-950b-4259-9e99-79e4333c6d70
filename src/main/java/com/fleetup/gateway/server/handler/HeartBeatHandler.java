package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.conn.HeartBeatMO;
import com.fleetup.gateway.event.HeartBeatEvent;
import io.netty.channel.Channel;

/**
 * @className: Heart<PERSON>eatHand<PERSON>
 * @author: Justin
 * @Date: 2024-05-20
 **/
public class HeartBeatHandler extends DefaultDataHandler {

    public HeartBeatHandler() {
        super();
    }

    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        //No operation is required
    }

    HeartBeatEvent buildEvent(HeartBeatMO data){
        HeartBeatEvent event = new HeartBeatEvent();
        return event;
    }


}
