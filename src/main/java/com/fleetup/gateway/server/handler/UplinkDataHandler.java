package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.constants.UploadTypeEnum;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.datastruct.transmission.*;
import com.fleetup.gateway.event.*;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.Hex;
import io.netty.channel.Channel;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @className: UplinkDataHandler
 * @author: Justin
 * @Date: 2024-05-28
 **/
public class UplinkDataHandler extends DefaultDataHandler {

    public UplinkDataHandler() {
        super();
    }

    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        UplinkData uplinkData = new UplinkData(protocol.getPayload().toBytes());
        if (uplinkData.getMessageType().getValue() != Constants.UPLINK_TRANSPARENT){
            logger.warn("This additional data type is not implemented");
            return;
        }
        F0Data data = new F0Data(uplinkData.getPayload().toBytes());
        int messageSubcategory = data.getMessageSubcategory().getValue();
        UploadTypeEnum value = UploadTypeEnum.getValue(messageSubcategory);
        if (value == null){
            logger.warn("No corresponding upload type found");
            return;
        }
        logger.info("Up Link Data Type: {}",value.getMessageInfo());
        if (messageSubcategory == UploadTypeEnum.UPLOAD_ALARM_AND_DRIVING_DATA.getValue()){
            AlarmDataReport alarmDataReport = new AlarmDataReport(data.getPayload().toBytes());
            GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_ALARM, deviceId, buildAlarmEvent(data, alarmDataReport)));
        } else if (messageSubcategory == UploadTypeEnum.MILEAGE_DATA_REPORTING.getValue()){
            //此数据才是trip结束后发送出来的准确数据， 但是consumer中并未使用

            TravelDataReport travelDataReport = new TravelDataReport(data.getPayload().toBytes());
            TravelDataEvent travelDataEvent = buildTripEvent(travelDataReport);

            if (travelDataEvent.getTripNumber() != 0 && travelDataEvent.getFlag() == 1){
                session.attr(Constants.ATTR_KEY_TRIP_ID).set(travelDataEvent.getTripNumber());
            }
            GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.TRAVEL_DATA_REPORT, deviceId,travelDataEvent));
        } else if (messageSubcategory == UploadTypeEnum.UPLOAD_OBD_DATA_STREAM.getValue()){
            //目前trip相关数据都是基于此数据计算而来

            OBDDataReport obdDataReport = new OBDDataReport(data.getPayload().toBytes());
            GpsEvent gpsEvent = buildGpsEvent(data, obdDataReport);
            if (gpsEvent.getAccStatus() == 0 ) return;
            Long tripNumber = session.attr(Constants.ATTR_KEY_TRIP_ID).get();
            if (gpsEvent.getTripNumber() != 0){
                if (tripNumber != null && (tripNumber > gpsEvent.getTripNumber())){
                    gpsEvent.setTripNumber(tripNumber);
                }
                session.attr(Constants.ATTR_KEY_TRIP_ID).set(gpsEvent.getTripNumber());
            }
            GpsEvent.Stat stat = gpsEvent.getStat();
            if (stat.getTotalFuel() > 0){
                session.attr(Constants.ATTR_KEY_CONSUMPTION_L).set(stat.getTotalFuel());
            }
            if (stat.getTotalTripMileage() > 0){
                session.attr(Constants.ATTR_KEY_ACCUMULATED_MILEAGE).set(stat.getTotalTripMileage());
            }

            if (gpsEvent.getRpm()[0] > 0){
                session.attr(Constants.ATTR_KEY_RPM_DATA).set(gpsEvent.getRpm()[0]);
            }
            GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_GPS, deviceId, gpsEvent));
        }else if(messageSubcategory == UploadTypeEnum.VIN_REPORTING.getValue()){
            VinData vinData = new VinData(data.getPayload().getMessage());
            if (vinData.getVin() != null){
                VinCodeEvent vinCodeEvent = new VinCodeEvent(vinData.getVin().toString());
                GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_VIN, deviceId, vinCodeEvent));
            }
        }else if(messageSubcategory == UploadTypeEnum.UPLOAD_TROUBLE_CODE_DATA.getValue()){
            DTCDataReport dtcDataReport = new DTCDataReport(data.getPayload().getMessage());
            if(dtcDataReport.getDataNumber().getValue() <= 0){
                return;
            }
            DtcEvent dtcEvent = new DtcEvent();
            boolean isSouth = Hex.isBitSet(dtcDataReport.getStatus().getValue(), 2);
            boolean isWest = Hex.isBitSet(dtcDataReport.getStatus().getValue(), 3);
            dtcEvent.setLatitude(Double.valueOf(dtcDataReport.getLatitude().getValue() * 3.6 * (isSouth?-1:1)).longValue());
            dtcEvent.setLongitude(Double.valueOf(dtcDataReport.getLongitude().getValue() * 3.6 * (isWest?-1:1)).longValue());
            String tmTime = GpsLocationHandler.getGpsDateTime(data.getTime().readValue());
            dtcEvent.setTmTime(Long.parseLong(tmTime));
            dtcEvent.setDtcs(new ArrayList<>());
            DTCData[] dtcDatas = dtcDataReport.getDtcDatas();
            for (int i = 0; i < dtcDataReport.getDataNumber().getValue(); i++){
                DTCData dtcData = dtcDatas[i];
                if (dtcData.getTroubleCount().getValue() <= 0) continue;
                for (DTC dtc : dtcData.getTroubleCodes()){
                    dtcEvent.getDtcs().add(new DtcEvent.DtcDetail(dtc.getCode().getValue(),dtc.getCarType().getValue(),new String(dtc.getCodeDecode()),dtc.getStatus().getValue()));
                }
            }
            GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_DTC, deviceId, dtcEvent));
        }
    }

    public GpsEvent buildGpsEvent(F0Data f0Data , OBDDataReport obdDataReport){
        GpsEvent event = new GpsEvent();
        long status = obdDataReport.getStatus().getValue();
        int accStatus = Hex.booleanAsInt(Hex.isBitSet(status, 0));
        event.setAccStatus(accStatus);
        event.setHistoryStatus(Hex.booleanAsInt(Hex.isBitSet(status,31)));
        GpsEvent.Stat stat = new GpsEvent.Stat();
        String acconTime = GpsLocationHandler.getGpsDateTime(f0Data.getTime().readValue());
        stat.setAcconTime(Long.parseLong(acconTime));
        stat.setDeviceTime(Long.parseLong(acconTime));
        Map<String, String> obdDetail = getPassengerDetail(obdDataReport.getObdData());
        if (obdDetail.containsKey("TripNumber")){
            event.setTripNumber(Integer.parseInt(obdDetail.get("TripNumber")));
        }
        if (obdDetail.containsKey("FuelConsumptionL")){
            stat.setTotalFuel(Long.parseLong(obdDetail.get("FuelConsumptionL")));
        }
        if (obdDetail.containsKey("AccumulatedMileage")){
            stat.setTotalTripMileage(Long.parseLong(obdDetail.get("AccumulatedMileage")));
        }
        if (obdDetail.containsKey("Odometer")){
            stat.setOdometer(obdDetail.get("Odometer"));
        }
        if (obdDetail.containsKey("EngineHour")){
            stat.setEngineHour(obdDetail.get("EngineHour"));
        }
        if (obdDetail.containsKey("CurrentBatteryVoltage")){
            stat.setBatteryVoltage((int) Double.parseDouble(obdDetail.get("CurrentBatteryVoltage")));
        }
        stat.setEngineDiagnoseProtocol(0);
        event.setStat(stat);
        GpsEvent.Gps[] gpsArr = new GpsEvent.Gps[1];
        GpsEvent.Gps gps = new GpsEvent.Gps();
        gps.setTmTime(Long.parseLong(acconTime));
        gps.setDevTime(Long.parseLong(acconTime));
        gps.setDate(Integer.parseInt(acconTime.substring(0,8)));
        gps.setTime(Integer.parseInt(acconTime.substring(8)));
        boolean isSouth = Hex.isBitSet(status, 2);
        boolean isWest = Hex.isBitSet(status, 3);
        gps.setLatitude(Double.valueOf(obdDataReport.getLatitude().getValue() * 3.6 * (isSouth?-1:1)).longValue());
        gps.setLongitude(Double.valueOf(obdDataReport.getLongitude().getValue() * 3.6 * (isWest?-1:1)).longValue());
        if (obdDetail.containsKey("Speed")){
            gps.setSpeed(Integer.parseInt(obdDetail.get("Speed")));
        }
        boolean isPositioned = Hex.isBitSet(status, 1);
        gps.setLocationStatus(isPositioned ? 3 : 0);
        gpsArr[0] = gps;
        event.setGps(gpsArr);
        int[] rpmData = new int[1];
        if (obdDetail.containsKey("RPM")){
            rpmData[0] = Integer.parseInt(obdDetail.get("RPM"));
        }
        event.setRpm(rpmData);
        return event;
    }

    private Map<String, String> getPassengerDetail(OBDdata[] obdData) {
        Map<String, String> result = new HashMap<>();
        for (OBDdata obdDatum : obdData) {
            int dataId = obdDatum.getDataId().getValue();
            if (dataId == OBDdata.TOTAL_MILEAGE){
                String value = String.valueOf(U32.valueOf(obdDatum.getValueArray()).getValue() * 0.1);
                result.put("Odometer",value);
            } else if (dataId == OBDdata.PASSENGER_FUEL_VOLUME){
                String value = String.valueOf(U8.valueOf(obdDatum.getValueArray()[0]).getValue());
                result.put("FuelVolume%",value);
            } else if (dataId == OBDdata.PASSENGER_FUEL_CONSUMPTION_L){
                String value = String.valueOf(U32.valueOf(obdDatum.getValueArray()).getValue());
                result.put("FuelConsumptionL",value);
            } else if (dataId == OBDdata.WATER_TEMPERATURE){
                String value = String.valueOf(U8.valueOf(obdDatum.getValueArray()[0]).getValue());
                result.put("WaterTemperature",value);
            } else if (dataId == OBDdata.VOLTAGE){
                String value = String.valueOf(U16.valueOf(obdDatum.getValueArray()).getValue() / 1000);
                result.put("CurrentBatteryVoltage",value);
            } else if (dataId == OBDdata.SPEED){
                String value = String.valueOf(U16.valueOf(obdDatum.getValueArray()).getValue() * 10000 / 3600);
                result.put("Speed",value);
            } else if (dataId == OBDdata.MILEAGE_ID){
                String value = String.valueOf(U32.valueOf(obdDatum.getValueArray()).getValue());
                result.put("TripNumber",value);
            } else if (dataId == OBDdata.ACCUMULATED_MILEAGE){
                String value = String.valueOf(U32.valueOf(obdDatum.getValueArray()).getValue() * 100);
                result.put("AccumulatedMileage",value);
            } else if (dataId == OBDdata.RPM){
                String value = String.valueOf(U16.valueOf(obdDatum.getValueArray()).getValue());
                result.put("RPM",value);
            }  else if (dataId == OBDdata.PASSENGER_FUEL_VOLUME_L){
                String value = String.valueOf(U16.valueOf(obdDatum.getValueArray()).getValue() * 100);
                result.put("FuelVolumeL",value);
            }  else if (dataId == OBDdata.COMMERCIAL_ENGINE_RUNNING_HOUR){
                String value = String.valueOf(U32.valueOf(obdDatum.getValueArray()).getValue());
                result.put("EngineHour",convertSecondsToHours(Long.parseLong(value)));
            }
        }
        return result;
    }

    public TravelDataEvent buildTripEvent(TravelDataReport data){
        TravelDataEvent event = new TravelDataEvent();
        int flag = data.getTravelProperty().getValue();
        event.setFlag(flag);
        event.setAcconTime(Long.parseLong(GpsLocationHandler.getGpsDateTime(data.getStartTime().readValue())));
        event.setTripNumber(data.getTravelNumber().getValue());
        if (flag == 2){
            int locationStatus = data.getLocationStatus().getValue();
            boolean startIsSouth = Hex.isBitSet(locationStatus, 0);
            boolean startIsWest = Hex.isBitSet(locationStatus, 1);
            boolean endIsSouth = Hex.isBitSet(locationStatus, 2);
            boolean endIsWest = Hex.isBitSet(locationStatus, 3);
            event.setEndTime(Long.parseLong(GpsLocationHandler.getGpsDateTime(data.getEndTime().readValue())));
            event.setStartLatitude(Double.valueOf(data.getStartLatitude().getValue() * 3.6 * (startIsSouth?-1:1)).longValue());
            event.setStartLongitude(Double.valueOf(data.getStartLongitude().getValue() * 3.6 * (startIsWest?-1:1)).longValue());
            event.setEndLatitude(Double.valueOf(data.getEndLatitude().getValue() * 3.6 * (endIsSouth?-1:1)).longValue());
            event.setEndLongitude(Double.valueOf(data.getEndLongitude().getValue() * 3.6 * (endIsWest?-1:1)).longValue());
            event.setTripMileage(data.getTripMileage().getValue() * 100);
            event.setTripFuel(data.getTripFuelConsumption().getValue());
        }
        return event;
    }


    public AlarmEvent buildAlarmEvent(F0Data f0Data, AlarmDataReport dataReport){
        AlarmEvent alarmEvent = new AlarmEvent();
        long status = dataReport.getStatus().getValue();
        GpsEvent.Stat stat = new GpsEvent.Stat();
        String acconTime = GpsLocationHandler.getGpsDateTime(f0Data.getTime().readValue());
        stat.setAcconTime(Long.parseLong(acconTime));
        stat.setDeviceTime(Long.parseLong(acconTime));
        GpsEvent.Gps gps = new GpsEvent.Gps();
        boolean isSouth = Hex.isBitSet(status, 2);
        boolean isWest = Hex.isBitSet(status, 3);
        gps.setLatitude(Double.valueOf(dataReport.getLatitude().getValue() * 3.6 * (isSouth?-1:1)).longValue());
        gps.setLongitude(Double.valueOf(dataReport.getLongitude().getValue() * 3.6 * (isWest?-1:1)).longValue());

        alarmEvent.setStat(stat);
        alarmEvent.setGps(gps);

        Alarm[] alarms = dataReport.getAlarms();
        List<AlarmEvent.Alarm> alarmList = buildAlarmData(alarms);
        AlarmEvent.Alarm[] alarmArray = new AlarmEvent.Alarm[alarmList.size()];
        alarmEvent.setAlarm(alarmList.toArray(alarmArray));
        return alarmEvent;

    }

    private List<AlarmEvent.Alarm> buildAlarmData(Alarm[] alarms) {
        List<AlarmEvent.Alarm> alarmList = new ArrayList<>();
        for (Alarm item : alarms) {
            int alarmDescription = 0;
            if (item.getDataLength().getValue() != 0){
                alarmDescription = Integer.parseInt(item.getAlarmValue().getValue());
            }
            if (item.getAlarmId().getValue() == 0x1A){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.HARD_ACCELERATION_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x01){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.POWER_ON_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x02){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.POWER_OFF_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x1B){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.HARD_DECELERATION_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x1C){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.SHARP_TURN_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x1D){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.QUICK_LANE_CHANGE_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x20){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.EMERGENCY_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x23){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.FATIGUE_DRIVE_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x25){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.SPEED_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x38){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.IGNITION_ON_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            } else if (item.getAlarmId().getValue() == 0x39){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.IGNITION_OFF_ALERT);
                alarm.setNewAlarmFlag(1);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            }  else if (item.getAlarmId().getValue() == 0x0E){
                AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
                alarm.setAlarmType(AlarmEvent.IDLING_ALERT);
                alarm.setNewAlarmFlag(0);
                alarm.setAlarmDescription(alarmDescription);
                alarmList.add(alarm);
            }
        }
        return alarmList;
    }

    private static String convertSecondsToHours(long seconds) {
        double hours = seconds / 3600.0;
        BigDecimal bd = new BigDecimal(hours).setScale(2, RoundingMode.HALF_UP);
        return bd.toString();
    }


}
