package com.fleetup.gateway.server.handler;

import com.fleetup.gateway.constants.HTConstants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public enum MessageHandlerMapping {

    CONN_MO_LOGIN(HTConstants.CONN_MO_LOGIN, new LoginHandler()),
    TERMINAL_GENERAL_RESPONSE_HANDLER(HTConstants.REPORTS_M0_GENERAL, new TerminalGeneralResponseHandler()),
    REPORTS_MO_GPS_HANDLER(HTConstants.REPORTS_MO_GPS, new GpsLocationHandler()),
    REPORTS_MO_UPLINK_HANDLER(HTConstants.REPORTS_MO_UPLINK, new UplinkDataHandler()),
    REGISTER_HANDLER(HTConstants.CONN_MO_REGISTRATION, new RegisterHandler()),
    UNREGISTER_HANDLER(HTConstants.CHNNEL_UNREGISTERED, new UnregisteredHandler()),
    HEARTBEAT(HTConstants.CONN_MO_HEARTBEAT, new HeartBeatHandler()),
    DEFAULT_HANDLER(HTConstants.DEFAULT, new DefaultDataHandler()),
    COMMAND_HANDLER(HTConstants.COMMAND_RESPONSE,new OnlineCommandHandler()),
    ;

    private int id;
    private DataHandler handler;

    MessageHandlerMapping(int id, DataHandler handler) {
        this.id = id;
        this.handler = handler;
    }

    public int id() {
        return id;
    }

    public DataHandler handler() {
        return handler;
    }

    public static MessageHandlerMapping valueOf(int id) {
        for (MessageHandlerMapping mapping : values()) {
            if (id == mapping.id) {
                return mapping;
            }
        }
        return null;
    }

    public static Map<Integer, DataHandler> asMap() {
        Map<Integer, DataHandler> handlers = new HashMap<>();
        Arrays.asList(values()).forEach(mapping -> handlers.put(mapping.id, mapping.handler));
        return handlers;
    }

    @Override
    public String toString() {
        return String.format("%s[id=%d,handler=%s]", name(), id(), handler());
    }
}
