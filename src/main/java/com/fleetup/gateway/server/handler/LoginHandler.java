package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.config.CommandRequest;
import com.fleetup.gateway.datastruct.conn.LoginMO;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.event.LoginEvent;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.MessageSeq;
import com.fleetup.gateway.util.ObjectUtils;
import io.netty.channel.Channel;

import java.time.LocalDateTime;

/**
 * @className: LoginHandler
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class LoginHandler extends DataHandler{
    private String[] commands = new String[]{"VERSION#", "PARAM#"};
    public LoginHandler() {
        super();
        commands = new Config().getProperties().getProperty(Config.Keys.DEFALUT_COMMANDS, "VERSION#|PARAM#|STATUS#|TIMER#").split("[|]");
    }

    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        logger.debug("devId : {}",deviceId);
        LoginMO data = new LoginMO(protocol.getPayload().toBytes());
        logger.debug("LOGIN:{}", ObjectUtils.toJSONString(data));
        GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_CONN_MO_LOGIN, deviceId, buildEvent(data)));
        sendOnlineCommandToGetDeviceParamters(session,deviceId);
    }

    LoginEvent buildEvent(LoginMO data){
        return new LoginEvent(LocalDateTime.now());
    }

    void sendOnlineCommandToGetDeviceParamters(Channel session, String devId){
        for (String commandContent : commands) {
            session.writeAndFlush(new BaseProtocol(devId, HTConstants.COMMAND_QUERY, new CommandRequest(commandContent),MessageSeq.getInstance().getNextSeq(devId)));
        }
    }



}

