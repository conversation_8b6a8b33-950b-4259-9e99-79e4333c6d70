package com.fleetup.gateway.server.handler;

import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.config.CommandRequest;
import com.fleetup.gateway.datastruct.config.CommandResponse;
import com.fleetup.gateway.event.CommandEvent;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.MessageSeq;
import io.netty.channel.Channel;

public class OnlineCommandHandler extends DataHandler {

    @Override
    protected void doAction(Channel session, String deviceId, BaseProtocol baseProtocol) {
        CommandResponse response = new CommandResponse(baseProtocol.getPayload().getMessage());
        CommandEvent event = new CommandEvent();
        event.setCommandContent(response.getCommandContent());
        event.setEncoding(response.getEncoding().getValue());
        event.setSequenceNum(response.getSequenceNum().getValue());
        GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_COMMAND,deviceId,event));
        if (event.getCommandContent().startsWith("OBDSET") && event.getCommandContent().contains("OK")){
            session.writeAndFlush(new BaseProtocol(deviceId, HTConstants.COMMAND_QUERY, new CommandRequest("OBDSET#"), MessageSeq.getInstance().getNextSeq(deviceId)));
        }
    }

}
