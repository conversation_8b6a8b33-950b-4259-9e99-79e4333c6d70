package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.PlatformGeneralResponse;
import com.fleetup.gateway.util.MessageSeq;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @className: DataHandler
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class DataHandler {
    protected final Logger logger = LoggerFactory.getLogger(DataHandler.class);

    protected static final MessageSeq SEQ = MessageSeq.getInstance();

    /**
     * Handle BaseProtocol, will call doResponse() and doAction() in sequence.
     *
     * @param session
     * @param deviceId
     * @param baseProtocol
     */
    public void handle(Channel session, String deviceId, BaseProtocol baseProtocol) {
        if (baseProtocol != null) {
            doResponse(session, deviceId, baseProtocol);
        }
        doAction(session, deviceId, baseProtocol);
    }

    /**
     * Handle Response<br>
     * Call doResponse() first, then doAction()<br>
     * Default implementation(Platform general response)
     *
     * @param session
     * @param deviceId
     * @param baseProtocol
     */
    protected void doResponse(Channel session, String deviceId, BaseProtocol baseProtocol) {
        session.writeAndFlush(new BaseProtocol(deviceId, HTConstants.REPORTS_MT_GENERAL,new PlatformGeneralResponse(baseProtocol.getSerialNumber().getValue(),baseProtocol.getMessageId().getValue(),0), SEQ.getNextSeq(deviceId)));
    }

    /**
     * Do your business here<br>
     * Call doResponse() first, then doAction()<br>
     *
     * @param session
     * @param deviceId
     * @param baseProtocol
     */
    protected void doAction(Channel session, String deviceId, BaseProtocol baseProtocol) {
        //Do nothing
    }
}
