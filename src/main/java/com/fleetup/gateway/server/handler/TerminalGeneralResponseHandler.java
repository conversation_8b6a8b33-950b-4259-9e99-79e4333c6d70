package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.TerminalGeneralResponse;
import com.fleetup.gateway.util.ObjectUtils;
import io.netty.channel.Channel;

/**
 * @className: TerminalGeneralResponse
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class TerminalGeneralResponseHandler extends DefaultDataHandler {

    public TerminalGeneralResponseHandler() {
        super();
    }

    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        TerminalGeneralResponse data = new TerminalGeneralResponse(protocol.getPayload().toBytes());
        logger.debug("TerminalGeneralResponse[{}] - {}", deviceId, ObjectUtils.toJSONString(data));
        if (data.getResult().getValue() == 0){
            logger.info("TerminalGeneralResponse : Success");
        } else {
            logger.warn("TerminalGeneralResponse : Fail");
        }
    }

    @Override
    public void doResponse(Channel session, String deviceId, BaseProtocol protocol) {
        //No need to reply
    }


}
