package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.datastruct.report.AdditionalInfo;
import com.fleetup.gateway.datastruct.report.LocationReport;
import com.fleetup.gateway.event.AlarmEvent;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.event.GpsEvent;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.ObjectUtils;
import io.netty.channel.Channel;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @className: GpsLocationHandler
 * @author: Justin
 * @Date: 2024-05-20
 **/
public class GpsLocation<PERSON>and<PERSON> extends DefaultDataHandler {

    public GpsLocationHandler() {
        super();
    }

    @Override
    public void doAction(Channel session, String deviceId, BaseProtocol protocol){
        LocationReport gpsData = new LocationReport(protocol.getPayload().toBytes());
        logger.debug("GPS DATA:{}", ObjectUtils.toJSONString(gpsData));
        GpsEvent gpsEvent = buildGpsEvent(gpsData);
        Long tripId = session.attr(Constants.ATTR_KEY_TRIP_ID).get();
        Long totalFuel = session.attr(Constants.ATTR_KEY_CONSUMPTION_L).get();
        Long totalTripMileage = session.attr(Constants.ATTR_KEY_ACCUMULATED_MILEAGE).get();
        Integer rpm = session.attr(Constants.ATTR_KEY_RPM_DATA).get();
        if (tripId != null){
            gpsEvent.setTripNumber(tripId);
        }
        if (totalFuel != null && gpsEvent.getStat().getTotalFuel() == 0){
            gpsEvent.getStat().setTotalFuel(totalFuel);
        }
        if (totalTripMileage != null && gpsEvent.getStat().getTotalTripMileage() == 0){
            gpsEvent.getStat().setTotalTripMileage(totalTripMileage);
        }
        if (rpm != null){
            int[] rpmData = new int[1];
            rpmData[0] = rpm;
            gpsEvent.setRpm(rpmData);
        }
        GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_GPS, deviceId, gpsEvent));
        AlarmEvent alarmEvent = buildAlarmEvent(gpsEvent, gpsData.getAlarmFlag().getValue());
        GatewayContext.me.publishEvent(new EventMessage<>(HTConstants.OBD_REPORTS_MO_ALARM, deviceId, alarmEvent));
    }

    public GpsEvent buildGpsEvent(LocationReport gpsData){
        GpsEvent event = new GpsEvent();
        long status = gpsData.getStatus().getValue();
        int accStatus = Hex.booleanAsInt(Hex.isBitSet(status, 0));
        event.setAccStatus(accStatus);
        event.setHistoryStatus(Hex.booleanAsInt(Hex.isBitSet(status,31)));
        GpsEvent.Stat stat = new GpsEvent.Stat();
        String acconTime = getGpsDateTime(gpsData.getTime().readValue());
        Map<String, String> additionDetail = getAdditionDetail(gpsData.getAdditionalInfoArray());
        stat.setAcconTime(Long.parseLong(acconTime));
        stat.setDeviceTime(Long.parseLong(acconTime));
        if (additionDetail.containsKey("CurrentFuel")){
            stat.setCurrentFuel(Long.parseLong(additionDetail.get("CurrentFuel")));
        }
        if (additionDetail.containsKey("TotalMileage")){
            stat.setTotalTripMileage(Long.parseLong(additionDetail.get("TotalMileage")));
        }
        stat.setEngineDiagnoseProtocol(0);

        int[] reserveArr = new int[8];

        if (additionDetail.containsKey("GsmSignal")){
            reserveArr[4] = Integer.parseInt(additionDetail.get("GsmSignal"));
        }
        stat.setReserve(reserveArr);
        event.setStat(stat);

        GpsEvent.Gps[] gpsArr = new GpsEvent.Gps[1];
        GpsEvent.Gps gps = new GpsEvent.Gps();
        gps.setTmTime(Long.parseLong(acconTime));
        gps.setDevTime(Long.parseLong(acconTime));
        gps.setDate(Integer.parseInt(acconTime.substring(0,8)));
        gps.setTime(Integer.parseInt(acconTime.substring(8)));
        boolean isSouth = Hex.isBitSet(status, 2);
        boolean isWest = Hex.isBitSet(status, 3);
        gps.setLatitude(Double.valueOf(gpsData.getLatitude().getValue() * 3.6 * (isSouth?-1:1)).longValue());
        gps.setLongitude(Double.valueOf(gpsData.getLongitude().getValue() * 3.6 * (isWest?-1:1)).longValue());
        gps.setSpeed(gpsData.getSpeed().getValue() * 10000 / 3600);
        gps.setDirection(gpsData.getDirection().getValue() * 10);
        boolean isPositioned = Hex.isBitSet(status, 1);
        gps.setLocationStatus(isPositioned ? 3 : 0);
        gpsArr[0] = gps;
        event.setGps(gpsArr);
        int[] rpmData = new int[1];
        event.setRpm(rpmData);
        return event;
    }

    public AlarmEvent buildAlarmEvent(GpsEvent data,long alarmFlag){
        AlarmEvent alarmEvent = new AlarmEvent();
        alarmEvent.setGps(data.getGps()[0]);
        alarmEvent.setStat(data.getStat());
        List<AlarmEvent.Alarm> alarmList = new ArrayList<>();
        if (Hex.isBitSet(alarmFlag,0)){
            AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
            alarm.setAlarmType(AlarmEvent.EMERGENCY_ALERT);
            alarm.setAlarmDescription(1);
            alarm.setNewAlarmFlag(1);
            alarmList.add(alarm);
        }
        if (Hex.isBitSet(alarmFlag,1)){
            AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
            alarm.setAlarmType(AlarmEvent.SPEED_ALERT);
            alarm.setAlarmDescription(data.getGps()[0].getSpeed());
            alarm.setNewAlarmFlag(1);
            alarmList.add(alarm);
        }
        if (Hex.isBitSet(alarmFlag,2)){
            AlarmEvent.Alarm alarm = new AlarmEvent.Alarm();
            alarm.setAlarmType(AlarmEvent.FATIGUE_DRIVE_ALERT);
            alarm.setAlarmDescription(1);
            alarm.setNewAlarmFlag(0);
            alarmList.add(alarm);
        }
        AlarmEvent.Alarm[] alarmArray = new AlarmEvent.Alarm[alarmList.size()];
        alarmEvent.setAlarm(alarmList.toArray(alarmArray));
        return alarmEvent;
    }

    private Map<String,String> getAdditionDetail(AdditionalInfo[] additionalInfos){
        Map<String, String> result = new HashMap<>();

        for (AdditionalInfo additionalInfo : additionalInfos) {
            int id = additionalInfo.getDataId().getValue();
            if (id == 0x01){
                String value = String.valueOf(U32.valueOf(additionalInfo.getValueArray()).getValue() * 100);
                result.put("TotalMileage",value);
            } else if (id == 0x02){
                String value = String.valueOf(U16.valueOf(additionalInfo.getValueArray()).getValue() * 10);
                result.put("CurrentFuel",value);
            } else if (id == 0x03){
                String value = String.valueOf(U16.valueOf(additionalInfo.getValueArray()).getValue() * 10000 / 3600);
                result.put("Speed",value);
            } else if (id == 0x2A){
                String value = String.valueOf(U16.valueOf(additionalInfo.getValueArray()).getValue());
                result.put("sleepMode",value);
            } else if (id == 0x30){
                String value = String.valueOf(U8.valueOf(additionalInfo.getValueArray()[0]).getValue());
                result.put("GsmSignal",value);
            } else if (id == 0x31){
                String value = String.valueOf(U8.valueOf(additionalInfo.getValueArray()[0]).getValue());
                result.put("SatellitesNumber",value);
            }
        }
        return result;
    }


    public static String getGpsDateTime(String dataTime){
        Date currentDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String yearPrefix = sdf.format(currentDate);
        return yearPrefix.substring(0,2) + dataTime;
    }

}
