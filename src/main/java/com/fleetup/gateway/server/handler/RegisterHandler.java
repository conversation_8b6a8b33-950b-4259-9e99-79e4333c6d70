package com.fleetup.gateway.server.handler;


import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.conn.RegisterMO;
import com.fleetup.gateway.datastruct.conn.RegisterMT;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.event.LoginEvent;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.ObjectUtils;
import io.netty.channel.Channel;

import java.time.LocalDateTime;

/**
 * 0x0100
 *
 * @className: RegisterHandler
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class RegisterHandler extends DataHandler {

    public final String baseKey = "ODY4OTM1MDYwMDI0NjE3";

    public RegisterHandler() {
        super();
    }

    @Override
    protected void doResponse(Channel session, String deviceId, BaseProtocol baseProtocol) {
        RegisterMT data = new RegisterMT(baseProtocol.getSerialNumber().getValue(), 0, baseKey);
        session.writeAndFlush(new BaseProtocol(deviceId, HTConstants.REPORTS_MT_REGISTRATION, data, SEQ.getNextSeq(deviceId)));
    }

    @Override
    protected void doAction(Channel session, String deviceId, BaseProtocol baseProtocol) {
        RegisterMO data = new RegisterMO(baseProtocol.getPayload().toBytes());
        logger.debug("REGISTER:{}", ObjectUtils.toJSONString(data));
        GatewayContext.me.publishEvent(new EventMessage<>(baseProtocol.getMessageId().getValue(), deviceId, buildEvent(data)));
    }

    LoginEvent buildEvent(RegisterMO data){
        return new LoginEvent(LocalDateTime.now());
    }
}
