package com.fleetup.gateway.server;


import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.plugin.Config;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.group.ChannelGroup;

/**
 * @className: IServer
 * @author: Justin
 * @Date: 2024-05-17
 **/
public interface IServer {

    void bind(final Config config) throws Exception;

    ServerBootstrap getIoAcceptor();

    ChannelGroup getChannels();

    void shutdown();

    void pushMessage(String devId, byte[] payload);

    void upgrade(String devId, String firmwareVersion, String firmwareLocation);

    void restoreUpgrade(String devId, String firmwareVersion, String firmwareLocation);

    int pushCommand(String devId, String command,int seqNum);
}

