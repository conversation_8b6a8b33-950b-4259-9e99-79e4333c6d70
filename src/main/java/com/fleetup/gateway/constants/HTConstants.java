package com.fleetup.gateway.constants;


/**
 * @className: HTConstants
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class HTConstants {

    /**
     * REGISTRATION
     */
    public static final int CONN_MO_REGISTRATION = 0X0100;
    public static final int REPORTS_MT_REGISTRATION = 0X8100;
    public static final int DEVICE_UNREGISTERED = 0x0003;

    /**
     * login
     */
    public static final int CONN_MO_LOGIN = 0x0102;

    /**
     * HEARTBEAT
     */
    public static final int CONN_MO_HEARTBEAT = 0x0002;

    /**
     * GENERAL REPORT
     */
    public static final int REPORTS_MT_GENERAL = 0X8001;
    public static final int REPORTS_M0_GENERAL = 0X0001;
    public static final int REPORTS_MO_GPS = 0x0200;
    public static final int REPORTS_MO_UPLINK = 0x0900;

    /**
     * CONFIG
     * @param args
     */
    public static final int CONFIG_MT_SET = 0x8103;
    public static final int CONFIG_MT_QUERY = 0x8106;
    public static final int CONFIG_MO_QUERY = 0x0104;

    public static final int COMMAND_QUERY = 0x8300;
    public static final int COMMAND_RESPONSE = 0x0300;


    public static final int DEFAULT = 0x0f0001;
    public static final int CHNNEL_UNREGISTERED = 0x0f0004;

    /**
     * Start/End Trip
     */
    public static final int TRAVEL_DATA_REPORT = 0xf004;


    /**
     * Protocol ID of OBD
     */
    public static final int OBD_REPORTS_MO_GPS = 0x4001;
    public static final int OBD_REPORTS_MO_ALARM = 0x4007;
    public static final int OBD_CONN_MO_LOGIN = 0x1001;
    public static final int OBD_CONN_MO_LOGOUT = 0x1002;


    /**
     * customize
     */
    public static final int OBD_REPORTS_MO_VIN = 90014;
    public static final int OBD_REPORTS_MO_DTC = 91014;
    public static final int OBD_REPORTS_MO_COMMAND = 40962;
//    public static final int OBD_REPORTS_MO_VIN = 0x0900;
//    public static final int OBD_REPORTS_MO_VIN = 0x0900;

}

