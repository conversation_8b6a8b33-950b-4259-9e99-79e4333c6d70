package com.fleetup.gateway.constants;


import io.netty.channel.Channel;
import io.netty.channel.group.ChannelGroup;
import io.netty.util.AttributeKey;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * @className: Constants
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class Constants {
    private Constants() {}

    public static final byte IDENTIFIER = 0x7E;
    public static final int UPLINK_TRANSPARENT = 0xF0;
    public static final byte[] GENERAL_RESPONSE = new byte[]{(byte) 0x80, 0x01};
    private static final String SESSION_ATTR_DEVICE_ID = "DEVICE_ID";
    private static final String SESSION_ATTR_TRIP_ID = "TRIP_ID";
    private static final String SESSION_ATTR_ACCUMULATED_MILEAGE = "ACCUMULATED_MILEAGE";
    private static final String SESSION_ATTR_CONSUMPTION_L = "ACCUMULATED_FUEL";
    private static final String SESSION_ATTR_RPM_DATA = "RPM_DATA";

    public static final AttributeKey<String> ATTR_KEY_DEV_ID = AttributeKey.valueOf(Constants.SESSION_ATTR_DEVICE_ID);
    public static final AttributeKey<Long> ATTR_KEY_TRIP_ID = AttributeKey.valueOf(Constants.SESSION_ATTR_TRIP_ID);
    public static final AttributeKey<Long> ATTR_KEY_ACCUMULATED_MILEAGE = AttributeKey.valueOf(Constants.SESSION_ATTR_ACCUMULATED_MILEAGE);
    public static final AttributeKey<Long> ATTR_KEY_CONSUMPTION_L = AttributeKey.valueOf(Constants.SESSION_ATTR_CONSUMPTION_L);
    public static final AttributeKey<Integer> ATTR_KEY_RPM_DATA = AttributeKey.valueOf(Constants.SESSION_ATTR_RPM_DATA);


    public static String getDeviceId(Channel channel){
        return channel.attr(Constants.ATTR_KEY_DEV_ID).get();
    }

    public static boolean online(ChannelGroup channels, String imei){
        return channels.stream()
                .map(Constants::getDeviceId)
                .anyMatch(imei::equals);
    }
    public static String getHost() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            return "0.0.0.0";
        }
    }

}
