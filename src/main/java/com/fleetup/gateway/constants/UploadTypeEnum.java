package com.fleetup.gateway.constants;


/**
 * <AUTHOR>
 */

public enum UploadTypeEnum {
    UPLOAD_OBD_DATA_STREAM(0x01, "OBD DATA"),
    UPLOAD_TROUBLE_CODE_DATA(0x02, "Trouble Code Data"),
    UPLOAD_ALARM_AND_DRIVING_DATA(0x03, "Alarm and Driving Data"),
    MILEAGE_DATA_REPORTING(0x04, "Mileage Data Reporting"),
    OBD_MCU_LOG_REPORTING(0x05, "OBD MCU Log Reporting"),
    UPLOAD_CAN_LEARNING_DATA(0x06, "CAN Learning Data"),
    UPLOAD_DATA_STREAM_ID_LIST(0x07, "Data Stream ID List"),
    UPLOAD_VEHICLE_CONTROL_ID_LIST(0x08, "Vehicle Control ID List"),
    UPLOAD_ALARM_AND_DRIVING_ID_LIST(0x09, "Alarm and Driving ID List"),
    UPLOAD_CRITICAL_VEHICLE_DATA(0x0A, "Critical Vehicle Data"),
    VIN_REPORTING(0x0B, "VIN Reporting"),
    REPORT_VEHICLE_CHECK_DATA(0x0C, "Vehicle Check Data"),
    REPORT_DEVICE_CHECK_DATA(0x0D, "Device Check Data"),
    REPORT_APN_DATA(0x0E, "APN Data"),
    REPORT_DEVICE_FUNCTIONALITY_LIST(0x0F, "Device Functionality List"),
    G_SENSOR_DATA_UPLOAD(0x10, "G-Sensor Data Upload"),
    UPLOAD_WEICHAI_ENGINE_DATA(0x11, "Weichai Engine Data Upload using SEA J1939"),
    UPLOAD_WEICHAI_ENGINE_CONFIGURATION_DATA(0x12, "Weichai Engine Configuration Data Upload"),
    EXTERNAL_DEVICE_DATA_UPLOAD(0x13, "External Device Data Upload"),
    EMERGENCY_SCENARIO_DATA_PACKET(0x15, "Data Packet for Emergency Scenarios");
    private final int value;
    private final String messageInfo;

    UploadTypeEnum(int value, String messageInfo) {
        this.value = value;
        this.messageInfo = messageInfo;
    }

    public int getValue() {
        return value;
    }

    public String getMessageInfo() {
        return messageInfo;
    }

    public static UploadTypeEnum getValue(int value) {

        for (UploadTypeEnum type : UploadTypeEnum.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}

