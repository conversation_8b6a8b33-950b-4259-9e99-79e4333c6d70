package com.fleetup.gateway.plugin;


import com.fleetup.gateway.exception.GatewayException;
import com.fleetup.gateway.util.ResourceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

/**
 * @className: Config
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class Config {
    private static final Logger CONSOLE_LOG = LoggerFactory.getLogger("console");
    private static final String DEFAULT_GATEWAY_CONFIG_FILENAME = "/gateway.properties";
    private static final String DEFAULT_PORT = "30700";
    private static final String DEFAULT_HTTP_PORT = "23010";
    private static final String DEFAULT_LOG_LEVEL = "INFO";
    private static final String DEFAULT_IDLE_TIME_IN_SECONDS = String.valueOf(10 * 60);
    private static final String DEFAULT_SERVER_HEARTBEAT_TIME_IN_SECONDS = String.valueOf(16 * 60);
    private static int count = 0;
    Properties properties;

    public Config() {
        this(DEFAULT_GATEWAY_CONFIG_FILENAME);
    }


    /**
     * Initialize with property file
     *
     * @param propertyFileName Example: gateway.properties
     */
    public Config(String propertyFileName) {
        String configInUserDir = System.getProperty("user.dir") + propertyFileName;
        File file = new File(configInUserDir);
        if (file.exists()) {
            if (count == 0){
                CONSOLE_LOG.info("Load configuration file: {}", configInUserDir);
            }
            properties = ResourceUtil.loadPropertyFile(new File(configInUserDir));
        } else {
            try {
                if (count==0){
                    CONSOLE_LOG.info("Load configuration from classpath: {}" ,propertyFileName);
                }
                properties = getResourceAsProperties(propertyFileName);
            } catch (IOException e) {
                throw new GatewayException("Can not find resource " + propertyFileName, e);
            }
        }
        if (count == 0){
            prettyPrint();
            count++;
        }
    }

    private void prettyPrint(){
        for (Map.Entry<Object, Object> entry : new TreeMap<>(properties).entrySet()) {
            String logContent;
            if (entry.getKey().toString().contains("password")) {
                logContent = String.format("%-40s: %s", entry.getKey(), "******");
            } else {
                logContent = String.format("%-40s: %s", entry.getKey(), entry.getValue());
            }
            CONSOLE_LOG.info(logContent);
        }
    }

    public Config(Properties properties){
        this.properties = properties;
    }

    public Properties getProperties() {
        return properties;
    }

    public int getPort(){
        return Integer.parseInt(properties.getProperty("gateway.server.port", DEFAULT_PORT));
    }

    public int getHttpPort(){
        return Integer.valueOf(properties.getProperty(Keys.HTTP_SERVICE_PORT, DEFAULT_HTTP_PORT));
    }

    public int getAdminPort(){
        return getPort() + 1;
    }

    /**
     *
     * @return seconds
     */
    public int getIdleTime(){
        return Integer.parseInt(properties.getProperty("gateway.server.idleTime", DEFAULT_IDLE_TIME_IN_SECONDS));
    }

    public int getHeartBeatTime(){
        return Integer.parseInt(properties.getProperty("gateway.server.heartBeat", DEFAULT_SERVER_HEARTBEAT_TIME_IN_SECONDS));
    }

    public String getLog(){
        return properties.getProperty("gateway.server.logLevel", DEFAULT_LOG_LEVEL);
    }

    public String getValue(String key){
        return properties.getProperty(key);
    }
    public String getValue(String key, String defaultValue){
        return properties.getProperty(key, defaultValue);
    }

    public boolean getValueAsBoolean(String key){
        return Boolean.parseBoolean(getValue(key));
    }
    public boolean getValueAsBoolean(String key, boolean defaultValue){
        return Boolean.parseBoolean(getValue(key, String.valueOf(defaultValue)));
    }

    public int getValueAsInt(String key){
        return Integer.parseInt(getValue(key));
    }
    public int getValueAsInt(String key, int defaultValue){
        return Integer.parseInt(getValue(key, String.valueOf(defaultValue)));
    }
    public long getValueAsLong(String key){
        return Long.parseLong(getValue(key));
    }
    public long getValueAsLong(String key, long defaultValue){
        return Long.parseLong(getValue(key, String.valueOf(defaultValue)));
    }


    private Properties getResourceAsProperties(String resource) throws IOException {
        Properties props = new Properties();
        InputStream in = Config.class.getResourceAsStream(resource);
        if (in == null){
            throw new IOException("Could not find resource " + resource);
        }
        props.load(in);
        in.close();
        return props;
    }

    /**
     *
     * Mapping configuration key in property file gateway.properties
     *
     * <AUTHOR> SZ Team
     *
     */
    public static final class Keys{
        private Keys() {}
        public static final String DEFALUT_COMMANDS = "gateway.events.login.defaultCommands";
        public static final String UPGRADE_CHECK_INTERVAL_SECONDS = "gateway.upgrade.check.interval";
        public static final String REDIS_ENABLE = "gateway.redis.enable";
        public static final String REDIS_ENDPOINT = "gateway.redis.endpoint";
        public static final String HTTP_SERVICE_ENABLE = "http.service.enable";
        public static final String HTTP_SERVICE_PORT = "http.service.port";
        public static final String HTTP_CACHE_ENDPOINT = "http.cache.endpoint";
    }
}
