package com.fleetup.gateway.plugin.http.handler;

import com.amazonaws.util.json.Jackson;
import com.fleetup.gateway.plugin.http.HttpServer;
import com.fleetup.gateway.plugin.http.model.HttpRequest;
import com.fleetup.gateway.plugin.http.model.HttpResponse;
import io.netty.handler.codec.http.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public abstract class HttpHandler {

    protected Logger log = LoggerFactory.getLogger("http");
    protected HttpMethod httpMethod;
    protected HttpServer httpServer;

    protected int HTTP_TIMEOUT = 10;

    protected HttpHandler(HttpMethod httpMethod,HttpServer httpServer){
        this.httpMethod = httpMethod;
        this.httpServer = httpServer;
    }

    protected interface ParameterKey{
        String imei = "imei";
        String date = "date";
        String imeis = "imeis";
        String sequence = "sequence";
        String command = "cmdContent";
        String timeout = "timeout";
        String offline = "offline";
    }


    boolean isAuthorize(String session){
//        log.info("session : {}",session);
        return true;
//        if (!StringUtils.hasValue(session)) return false;
//        return Authentication.check(session);
    }
    public HttpResponse handler(HttpRequest req){
        if(!req.getMethod().equals(httpMethod)) return HttpResponse.MethodNotAllowed();
        if (!isAuthorize(req.getToken())) return HttpResponse.Unauthorized();
        try{
            log.debug("params : {}", Jackson.toJsonString(req.getQueryParams()));
            return action(req);
        }catch (Exception e){
            log.error("Handle request {} error", req.getUrl(), e);
            return HttpResponse.ServiceUnavailable(e);
        }

    }
    abstract HttpResponse action(HttpRequest req) throws Exception;
}
