package com.fleetup.gateway.plugin.http;

import com.amazonaws.util.json.Jackson;
import com.fleetup.gateway.plugin.http.handler.CommandHandler;
import com.fleetup.gateway.plugin.http.handler.ConsoleHandler;
import com.fleetup.gateway.plugin.http.handler.HttpHandler;
import com.fleetup.gateway.plugin.http.handler.StreamHandler;
import com.fleetup.gateway.plugin.http.model.HttpRequest;
import com.fleetup.gateway.plugin.http.model.HttpResponse;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.fleetup.gateway.plugin.http.constant.HTTP.*;
import static io.netty.handler.codec.http.HttpUtil.is100ContinueExpected;

@Sharable
public class MessageTransferHandler extends SimpleChannelInboundHandler<FullHttpRequest>{
	private static final Logger logger = LoggerFactory.getLogger("http");
	private final Map<String, HttpHandler> HANDLER_MAPPING = new HashMap<>();
	private final ConcurrentHashMap<String, List<Channel>> sseConnections = new ConcurrentHashMap<>();


	public MessageTransferHandler(HttpServer httpServer){
		HANDLER_MAPPING.put(URL_COMMAND,new CommandHandler(HttpMethod.POST,httpServer));
		HANDLER_MAPPING.put(URL_CONSOLE,new ConsoleHandler(HttpMethod.POST,httpServer));
		HANDLER_MAPPING.put(URL_STREAM,new StreamHandler(HttpMethod.POST,httpServer));
	}

	@Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest req) throws Exception {
		if (is100ContinueExpected(req)) {
			ctx.write(new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.CONTINUE));
		}
//		else{
//			sendSseHeadersAndKeepAlive(ctx);
//		}
		logger.info("path : {}, method : {}",req.uri(),req.getMethod().toString());
		boolean access = false;
		for(Map.Entry<String,HttpHandler> entry : HANDLER_MAPPING.entrySet()){
			if (req.uri().contains(entry.getKey())) {
				access = true;
				HttpResponse resp = entry.getValue().handler(new HttpRequest(req));
				if (resp.getHeader() != null && CONTENT_OCTEX_STREAM.equals(resp.getHeader().get(HttpHeaderNames.CONTENT_TYPE))){
					response(ctx, resp.getStatus(), resp.getHeader(),resp.getBody());
				}else{
					response(ctx, resp.getStatus(), Jackson.toJsonString(resp.getBody()));
				}
				break;
			}else if(req.uri().contains("stream")){
				access = true;
				String imei = new HttpRequest(req).getQueryParams().get("imei");
				if (!sseConnections.containsKey(imei)){
					sseConnections.put(imei,new ArrayList<>());
				}
				sseConnections.get(imei).add(ctx.channel());
			}
		}
		if (!access) response(ctx,HttpResponseStatus.NOT_FOUND,"Not found");

    }

	@Override
	public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
		if (IdleStateEvent.class.isAssignableFrom(evt.getClass())) {
			IdleStateEvent event = (IdleStateEvent) evt;
			if (event.state() == IdleState.ALL_IDLE) {
				logger.warn("Idling triggered, disconnected by server:{}, event:{}", ctx, event);
				ctx.close();
			}
		}
	}


	private void sendSseHeadersAndKeepAlive(ChannelHandlerContext ctx) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
		response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/event-stream; charset=UTF-8");
		response.headers().set(HttpHeaderNames.CACHE_CONTROL, "no-cache");
		response.headers().set(HttpHeaderNames.CONNECTION, "keep-alive");
		response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
		ctx.writeAndFlush(response);
	}

	private void sendSseMessage(Channel channel, String message) {
		if (channel.isActive()) {
			String sseMessage = "data: " + message + "\n\n";
			ByteBuf buf = Unpooled.copiedBuffer(sseMessage, StandardCharsets.UTF_8);
			channel.writeAndFlush(buf);
		}
	}


	private void response(ChannelHandlerContext ctx,HttpResponseStatus status,String msg){
		response( ctx, status, CONTENT_JSON, msg);
	}
	private void response(ChannelHandlerContext ctx,HttpResponseStatus status,String contentType,String msg){
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,status,Unpooled.copiedBuffer(msg, CharsetUtil.UTF_8));
		response.headers().set(HttpHeaderNames.CONTENT_TYPE, contentType);
		response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
		ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
	}

	private void response(ChannelHandlerContext ctx,HttpResponseStatus status,HttpHeaders headers,Object obj){
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,status,(ByteBuf) obj,false,false);
		response.headers().add(headers);
		ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
	}
    
}
