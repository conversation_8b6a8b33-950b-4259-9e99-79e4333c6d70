package com.fleetup.gateway.plugin.http;

import com.amazonaws.util.StringUtils;
import com.fleetup.gateway.constants.HTConstants;
import com.fleetup.gateway.datastruct.BaseProtocol;
import com.fleetup.gateway.datastruct.config.CommandRequest;
import com.fleetup.gateway.event.CommandEvent;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.IDataPublisher;
import com.fleetup.gateway.plugin.IDataPublisherCallback;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.plugin.http.remote.queue.Queue;
import com.fleetup.gateway.plugin.http.remote.queue.QueueType;
import com.fleetup.gateway.plugin.http.remote.queue.RemoteCommandRedisQueue;
import com.fleetup.gateway.server.IServer;
import com.fleetup.gateway.server.vl502.GatewayContext;
import com.fleetup.gateway.util.Hex;
import com.fleetup.gateway.util.MessageSeq;
import com.fleetup.gateway.util.ObjectUtils;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.fleetup.gateway.constants.Constants.getDeviceId;
import static com.fleetup.gateway.constants.Constants.getHost;
import static com.fleetup.gateway.plugin.http.constant.HTTP.COMMAND_PLACEHOLDER;

public class HttpPlugin implements IDataPublisher {
	private static final Logger LOG = LoggerFactory.getLogger("http");
	private static final Logger remote = LoggerFactory.getLogger("remote");
	private Config config;
	private HttpServer httpServer;
	private RedisClient redisClient;
	private IServer mainServer;
	private boolean isStarted = false;
	private ExecutorService executorService;

	public HttpPlugin() {}

	@Override
	public boolean start(Properties properties) {
		this.config =  new Config(properties);
		if (config.getValueAsBoolean(Config.Keys.HTTP_SERVICE_ENABLE, true)) {
            LOG.info("[{}] started", getClass().getSimpleName());
			redisClient = RedisClient.create(RedisURI.create(config.getValue(Config.Keys.HTTP_CACHE_ENDPOINT)));
			isStarted = true;
		} else {
            LOG.info("[{}] disabled", getClass().getSimpleName());
		}
		return isStarted;
	}

	@Override
	public <T> void publishRecord(EventMessage<T> record) {
		QueueService queueService = httpServer.getQueueService();
		if(record.getProtocolId() == HTConstants.OBD_REPORTS_MO_COMMAND){
			CommandEvent event = (CommandEvent)record.getData();
			remote.debug("devId : {}, responseCommand : {}",record.getDevId(), ObjectUtils.toJSONString(event));
			LOG.debug("isRegistered : {}, isIgnore : {} ",queueService.isRegistered(record.getDevId()),queueService.isIgnore(record.getDevId()));
			//real-time command response
			if (queueService.isRegistered(record.getDevId()) && !queueService.isIgnore(record.getDevId())){
				boolean b = RemoteCommandRedisQueue.build(queueService).withName(record.getDevId()).push(ObjectUtils.toJSONString(event));
				if (!b) LOG.warn("Failed to write message to queue. message : {}",ObjectUtils.toJSONString(event));
			}
		}else if (record.getProtocolId() == HTConstants.OBD_CONN_MO_LOGIN
//				|| record.getProtocolId() == HTConstants.REPORTS_MO_GPS
//				|| record.getProtocolId() == HTConstants.REPORTS_MO_UPLINK
		){
			LOG.info("Register {} as online",record.getDevId());
			queueService.active(record.getDevId());
		} else if (record.getProtocolId() == HTConstants.CHNNEL_UNREGISTERED) {
			LOG.info("Register {} as offline",record.getDevId());
			queueService.inactive(record.getDevId());
		}

	}

	@Override
	public boolean stop() {
		if (httpServer != null){
			LOG.info("clean all online device before stop server");
			Map<String,String> onlines = httpServer.getQueueService().allActive();
			if (onlines != null) {
				onlines.forEach((k,v) -> {
					httpServer.getQueueService().inactive(k);
				});
			}
			httpServer.shutdown();
		}
		if (executorService != null) {
			executorService.shutdown();
		}
        LOG.info("[{}] stopped", getClass().getSimpleName());
		return true;
	}

	@Override
	public IDataPublisherCallback getCallback() {
		if (!isStarted) return null;
		return server -> {
			mainServer = server;
			executorService = Executors.newSingleThreadExecutor();
			executorService.execute(() -> {
				httpServer = new HttpServer(server,redisClient);
				try {
					httpServer.startServer(config);
				} catch (Exception e) {
					LOG.error("Start HttpServer failed", e);
				}
			});
			GatewayContext.me.getWorker().register(()-> mainServer.getChannels().forEach(channel -> {
				String devId = getDeviceId(channel);

                if (StringUtils.hasValue(devId)){
					String fullId = Hex.getAllDevId(devId);
                    Queue queue = RemoteCommandRedisQueue.build(httpServer.getQueueService()).withType(QueueType.BATCH).withName(fullId);
                    while(queue.size() > 0){
                        String command = queue.pop();
                        if (StringUtils.hasValue(command)){
                            remote.info("Offline Command : {} >>> {}",fullId,command);
                            if (command.contains(COMMAND_PLACEHOLDER)){
                                String _command = command.split(COMMAND_PLACEHOLDER)[0];
                                int seq = Integer.parseInt(command.split(COMMAND_PLACEHOLDER)[1]);
                                mainServer.pushMessage(devId, new BaseProtocol(devId, HTConstants.COMMAND_QUERY, new CommandRequest(_command), seq).getMessage());
                            }else{
                                mainServer.pushMessage(devId, new BaseProtocol(devId, HTConstants.COMMAND_QUERY, new CommandRequest(command), MessageSeq.getInstance().getNextSeq(devId)).getMessage());
                            }
                        }
                    }
                }
            }),10);
			GatewayContext.me.getWorker().register(()->{
				Map<String,String> onlines = httpServer.getQueueService().allActive();
				if (onlines == null || onlines.size() == 0) return;

				List<String> devices = new ArrayList<>();
				mainServer.getChannels().forEach(channel -> {
					String devId = Hex.getAllDevId(getDeviceId(channel));
					if (!devices.contains(devId)) devices.add(devId);
				});
				onlines.forEach((k,v) -> {
					if (!devices.contains(k)){
						httpServer.getQueueService().inactive(k);
					}
				});
			},30);
		};
	}
}
