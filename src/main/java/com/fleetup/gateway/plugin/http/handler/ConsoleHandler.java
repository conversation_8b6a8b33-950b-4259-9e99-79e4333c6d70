package com.fleetup.gateway.plugin.http.handler;

import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.plugin.http.HttpServer;
import com.fleetup.gateway.plugin.http.model.HttpRequest;
import com.fleetup.gateway.plugin.http.model.HttpResponse;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.server.Version;
import io.netty.handler.codec.http.HttpMethod;

import java.io.File;
import java.nio.file.Files;
import java.util.*;

public class ConsoleHandler extends HttpHandler{


    private static final String URL_VERSION = "/version";
    private static final String URL_RAWDATA = "/rawdata";
    private static final String URL_DEVICES = "/devices";
    private static final String URL_STREAM = "/stream";

    private static final String FILE_PATH = "/opt/fleetup/gateway/rawdata/vl502/%s/%s_%s.txt";

    private QueueService queueService;
    public ConsoleHandler(HttpMethod httpMethod, HttpServer httpServer) {
        super(httpMethod, httpServer);
        queueService = httpServer.getQueueService();
    }

    @Override
    HttpResponse action(HttpRequest req) throws Exception {
        Map<String,String> params = req.getQueryParams();
        log.debug("params : {}",params);
        Map<String,Object> result = new HashMap<>();
        result.put("ResponseInstance", Constants.getHost());
        if (req.getUrl().contains(URL_VERSION)){
            result.put("data",Version.getVersion());
        }else if (req.getUrl().contains(URL_DEVICES)){
            Map<String,String> all = queueService.allActive();
            Map<String,Set<String>> group = new HashMap<>();
            if (all != null)
                all.forEach((k,v) -> {
                    if (!group.containsKey(v)) group.put(v,new HashSet<>());
                    group.get(v).add(k);
                });
            result.put("data",group);
        }else if (req.getUrl().contains(URL_RAWDATA)){
            if (!params.containsKey(ParameterKey.imei))return HttpResponse.BadRequest(ParameterKey.imei);
            if (!params.containsKey(ParameterKey.date))return HttpResponse.BadRequest(ParameterKey.date);
            String date = params.get(ParameterKey.date);
            String imei = params.get(ParameterKey.imei);
            String fileName = String.format(FILE_PATH, date,imei,date);
            log.debug("rawdata file name : {}",fileName);
            File file = new File(fileName);
            if (!file.exists()){
                result.put("data","No data");
                return HttpResponse.OK(result);
            }
            List<String> lines = Files.readAllLines(file.toPath());
            result.put("data",lines);
        }else if(req.getUrl().contains(URL_STREAM)){
            if (!params.containsKey(ParameterKey.imei))return HttpResponse.BadRequest(ParameterKey.imei);







        }else{
            return HttpResponse.NotFound();
        }
        return HttpResponse.OK(result);
    }
}
