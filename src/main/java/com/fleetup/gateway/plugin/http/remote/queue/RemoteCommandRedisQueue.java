package com.fleetup.gateway.plugin.http.remote.queue;

import com.amazonaws.util.StringUtils;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.util.DateUtils;
import io.lettuce.core.KeyValue;
import io.lettuce.core.RedisClient;
import io.lettuce.core.ScoredValue;
import io.lettuce.core.api.StatefulRedisConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RemoteCommandRedisQueue implements Queue {

    private static final Logger log = LoggerFactory.getLogger(RemoteCommandRedisQueue.class);

    private String name;
    private QueueType queueType = QueueType.ON_LINE;
    private RedisClient redisClient;

    private RemoteCommandRedisQueue(RedisClient redisClient){
        this.redisClient = redisClient;
    }

    public static final RemoteCommandRedisQueue build(QueueService queueService){
        return new RemoteCommandRedisQueue(queueService.getRedisClient());
    }

    public RemoteCommandRedisQueue withName(String name){
        this.name = name;
        return this;
    }
    public RemoteCommandRedisQueue withType(QueueType queueType){
        this.queueType = queueType;
        return this;
    }

    @Override
    public boolean push(String msg) {
        log.debug("push data , queue : {},value : {}",key(),msg);
        try(StatefulRedisConnection<String, String> connect = redisClient.connect()){
            if (queueType == QueueType.CHANNEL){
                connect.async().publish(key(), msg);
            }else if (queueType == QueueType.BATCH){
                if (size()>100) return false;
                connect.sync().zadd(key(), DateUtils.now(), msg);
            }else{
                connect.sync().lpush(key(),msg);
                connect.async().expire(key(),10);
            }
            return true;
        }
    }

    @Override
    public String pop() {
        try(StatefulRedisConnection<String, String> connect = redisClient.connect()) {
            KeyValue<String, ScoredValue<String>> keyValue = connect.sync().bzpopmin(10, key());
            if (keyValue == null) return null;
            else if (keyValue.getValue() == null) return null;
            else return keyValue.getValue().getValue();
        }
    }

    @Override
    public String pop(long timeout) {
        log.debug("pop value , queue : {} ",key());
        try(StatefulRedisConnection<String, String> connect = redisClient.connect()) {
            KeyValue<String, String> kv = connect.sync().brpop(timeout, key());
            return kv != null ? kv.getValue() : null;
        }
    }

    @Override
    public long size() {
        try(StatefulRedisConnection<String, String> connect = redisClient.connect()) {
            if (queueType == QueueType.BATCH) {
                return connect.sync().zcard(key());
            }
            return connect.sync().llen(key());
        }
    }

    private String key(){
        if (queueType == QueueType.CHANNEL) return queueType.getValue();
        if (!StringUtils.hasValue(name)) throw new NullPointerException("'name' is empty");
        return queueType.getValue() + name;
    }
}
