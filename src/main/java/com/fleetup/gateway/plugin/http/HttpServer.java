package com.fleetup.gateway.plugin.http;

import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.server.IServer;
import io.lettuce.core.RedisClient;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.cors.CorsConfig;
import io.netty.handler.codec.http.cors.CorsConfigBuilder;
import io.netty.handler.codec.http.cors.CorsHandler;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.GlobalEventExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;


public class HttpServer implements IServer {
	private static final Logger LOG = LoggerFactory.getLogger("http");
	IServer mainServer;
	QueueService queueService;
	ChannelGroup channels = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
	final ServerBootstrap serverBootstrap = new ServerBootstrap();
	MessageTransferHandler messageTransferHandler;
	EventLoopGroup bossGroup = null;
	EventLoopGroup workerGroup = null;

	public HttpServer(IServer server, RedisClient redisClient) {
		this.mainServer = server;
		this.queueService = new QueueService(server,redisClient);
		this.messageTransferHandler = new MessageTransferHandler(this);
	}

	public void startServer(Config config) throws InterruptedException {
		queueService.start();
		bind(config);
	}
	
	@Override
	public void bind(final Config config) throws InterruptedException {
		LOG.info("HTTP Server initializing....");
		bossGroup = new NioEventLoopGroup();
		workerGroup = new NioEventLoopGroup();
		try {
			serverBootstrap.group(bossGroup, workerGroup)
					.channel(NioServerSocketChannel.class)
					.handler(new LoggingHandler(LogLevel.DEBUG))
					.childHandler(new ChannelInitializer<SocketChannel>() {
						@Override
						protected void initChannel(SocketChannel ch) {
							ChannelPipeline pipeline = ch.pipeline();
							pipeline.addLast("idleStateHandler", new IdleStateHandler(0, 0, 30, TimeUnit.SECONDS));
							pipeline.addLast(new HttpServerCodec());
							pipeline.addLast("httpAggregator",new HttpObjectAggregator(1024*10));
						    pipeline.addLast(messageTransferHandler);
							CorsConfig corsConfig = CorsConfigBuilder.forAnyOrigin().allowNullOrigin().allowCredentials().build();
							pipeline.addLast(new CorsHandler(corsConfig));
						}
					}).option(ChannelOption.SO_BACKLOG, 128).childOption(ChannelOption.SO_KEEPALIVE, true);
			
			LOG.info("serverBootstrap.config: {}", serverBootstrap.config());
			ChannelFuture channelFuture = serverBootstrap.bind(config.getHttpPort()).sync();
			LOG.info("Http Server started successfully, running on port: {}", config.getHttpPort());
			channelFuture.channel().closeFuture().sync();
		} finally {
			bossGroup.shutdownGracefully();
			workerGroup.shutdownGracefully();
		}
	}
	
	@Override
	public ChannelGroup getChannels() {
		return channels;
	}
	
	@Override
	public ServerBootstrap getIoAcceptor() {
		return serverBootstrap;
	}


	@Override
	public void shutdown() {
		bossGroup.shutdownGracefully();
		workerGroup.shutdownGracefully();
		queueService.close();
	}

	@Override
	public void pushMessage(String devId, byte[] payload) {
		throw new RuntimeException("Not implemented");
	}

	@Override
	public void upgrade(String devId, String firmwareVersion, String firmwareLocation) {

	}

	@Override
	public void restoreUpgrade(String devId, String firmwareVersion, String firmwareLocation) {

	}

	@Override
	public int pushCommand(String devId, String command, int seqNum) {
		return 0;
	}


	public IServer getMainServer() {
		return mainServer;
	}

	public QueueService getQueueService() {
		return queueService;
	}

}

