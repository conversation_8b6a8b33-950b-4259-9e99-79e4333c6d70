package com.fleetup.gateway.plugin.http.remote;

import com.amazonaws.util.StringUtils;
import com.fleetup.gateway.plugin.http.remote.queue.QueueType;
import com.fleetup.gateway.server.IServer;
import com.fleetup.gateway.util.Hex;
import io.lettuce.core.RedisClient;
import io.lettuce.core.SetArgs;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.pubsub.RedisPubSubAdapter;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static com.fleetup.gateway.constants.Constants.getDeviceId;
import static com.fleetup.gateway.constants.Constants.getHost;
import static com.fleetup.gateway.plugin.http.constant.Contants.*;
import static com.fleetup.gateway.plugin.http.constant.HTTP.COMMAND_PLACEHOLDER;

public class QueueService {

    private static final Logger log = LoggerFactory.getLogger("remote");

    private final IServer server;

    private RedisClient redisClient;
    private StatefulRedisConnection<String, String> connection;
    private final StatefulRedisPubSubConnection<String, String> pubSubConnection;

    public QueueService(IServer server, RedisClient redisClient){
        this.server = server;
        this.redisClient = redisClient;
        this.connection = redisClient.connect();
        this.pubSubConnection = redisClient.connectPubSub();
    }


    public void start(){
        log.info(" QueueService starting...");
        pubSubConnection.addListener(new RedisPubSubAdapter<String,String>(){
            @Override
            public void message(String channelName, String message) {
                log.debug(">>>>>>channel : {}, message : {}",channelName,message);
                if (!StringUtils.hasValue(message) || !message.contains(COMMAND_PLACEHOLDER) || message.split(COMMAND_PLACEHOLDER).length != 3){
                    log.warn("message : '{}' is invalid",message);
                    return;
                }
                String devId = message.split(COMMAND_PLACEHOLDER)[0];
                String command = message.split(COMMAND_PLACEHOLDER)[1];
                int seqNum = Integer.parseInt(message.split(COMMAND_PLACEHOLDER)[2]);
                for (Channel channel : server.getChannels()){
                    if (getDeviceId(channel).equals(Hex.revertDevId(devId)) && channel.isActive()){
                        server.pushCommand(Hex.revertDevId(devId),command, seqNum);
                    }
                }
            }
        });
        pubSubConnection.sync().subscribe(QueueType.CHANNEL.getValue());
    }


    public StatefulRedisConnection<String, String> getConnection(){
        if (connection == null || !connection.isOpen())
            connection = redisClient.connect();
        return connection;
    }

    public void close(){
        if (connection != null) connection.close();
        if (redisClient != null) redisClient.shutdown();
    }

    public boolean register(String imei,int timeout){
        String str = getConnection().sync().set(imei,FLAG_KEY_LOCK, SetArgs.Builder.nx().ex(timeout));
        if(log.isDebugEnabled()) log.debug("device : {}, lock : {}",imei,str);
        return STATUS_OK.equals(str);
    }

    public boolean isRegistered(String imei){
        return FLAG_KEY_LOCK.equals(getConnection().sync().get(imei));
    }


    public void ignore(String imei,int timeout){
        getConnection().sync().set(FLAG_KEY_IGNORE_PRIFIX+imei,FLAG_KEY_IGNORE, SetArgs.Builder.nx().ex(timeout));
    }
    public boolean isIgnore(String imei){
        return FLAG_KEY_IGNORE.equals(getConnection().sync().get(FLAG_KEY_IGNORE_PRIFIX+imei));
    }

    public void release(String imei){
        getConnection().sync().del(imei,FLAG_KEY_IGNORE_PRIFIX+imei);
    }

    public void active(String imei){
        boolean status = getConnection().sync().hset(ON_LINE_LIST_KEY,imei,getHost());
        if (log.isDebugEnabled())log.debug("set {} online. host : {} >> {}",imei,getHost(),status);
    }

    public void inactive(String imei){
        String value = getConnection().sync().hget(ON_LINE_LIST_KEY,imei);
        //避免集群环境下误删
        if (getHost().equals(value)){
            getConnection().sync().hdel(ON_LINE_LIST_KEY,imei);
        }
    }

    public boolean isActive(String imei){
        String value = getConnection().sync().hget(ON_LINE_LIST_KEY,imei);
        log.info("device : {} remote ip : {}",imei,value);
        return StringUtils.hasValue(value);
    }

    public Map<String,String> allActive(){
        return getConnection().sync().hgetall(ON_LINE_LIST_KEY);
    }

    public RedisClient getRedisClient() {
        return redisClient;
    }
}
