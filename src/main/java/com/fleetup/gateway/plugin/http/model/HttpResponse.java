package com.fleetup.gateway.plugin.http.model;


import io.netty.handler.codec.http.DefaultHttpHeaders;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpResponseStatus;

import java.util.Map;

public class HttpResponse<T> {

    private HttpResponseStatus status;
    private HttpHeaders header;
    private Body<T> body;



    public static class Body<T>{

        private long timestamp = System.currentTimeMillis();
        private boolean success;
        private T data;

        public Body(){}

        public Body(T data){
            this.data = data;
        }

        public Body(boolean success,T data){
            this.success = success;
            this.data = data;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public T getData() {
            return data;
        }

        public void setData(T date) {
            this.data = data;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    public HttpResponse(){}

    public HttpResponse(HttpResponseStatus status,boolean success,T data){
        this.status = status;
        this.body = new Body<>(success,data);
    }

    public static HttpResponse OK(){
        return new HttpResponse(HttpResponseStatus.OK,true,null);
    }

    public static <T> HttpResponse OK(T data){
        return new HttpResponse(HttpResponseStatus.OK,true,data);
    }

    public static <T> HttpResponse OK(boolean success,T data){
        return new HttpResponse(HttpResponseStatus.OK,success,data);
    }

    public static HttpResponse BadRequest(String key){
        return new HttpResponse(HttpResponseStatus.BAD_REQUEST,false,key + " is invalid");
    }

    public static HttpResponse Unauthorized(){
        return new HttpResponse(HttpResponseStatus.UNAUTHORIZED,false,"Authorization failed");
    }

    public static HttpResponse NotFound(){
        return new HttpResponse(HttpResponseStatus.NOT_FOUND,false,"Resource does not exist");
    }

    public static HttpResponse MethodNotAllowed(){
        return new HttpResponse(HttpResponseStatus.METHOD_NOT_ALLOWED,false,"Method Not Allowed");
    }

    public static HttpResponse ServiceUnavailable(Exception e){
        return new HttpResponse(HttpResponseStatus.SERVICE_UNAVAILABLE,false,e);
    }

    public HttpResponseStatus getStatus() {
        return status;
    }

    public void setCode(HttpResponseStatus status) {
        this.status = status;
    }

    public HttpHeaders getHeader() {
        return header;
    }

    public void setHeader(Map<CharSequence,Object> header) {
        HttpHeaders headers = new DefaultHttpHeaders();
        header.forEach(headers::add);
        this.header = headers;
    }

    public HttpResponse withHeader(Map<CharSequence,Object> header) {
        HttpHeaders headers = new DefaultHttpHeaders();
        header.forEach(headers::add);
        this.header = headers;
        return this;
    }

    public Body<T> getBody() {
        return body;
    }



    public void setBody(Body<T> body) {
        this.body = body;
    }

    public HttpResponse withBody(Body<T> body) {
        this.body = body;
        return this;
    }
}
