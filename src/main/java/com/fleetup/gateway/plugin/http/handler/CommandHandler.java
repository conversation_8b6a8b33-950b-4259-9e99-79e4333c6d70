package com.fleetup.gateway.plugin.http.handler;

import com.amazonaws.util.StringUtils;
import com.fleetup.gateway.plugin.http.HttpServer;
import com.fleetup.gateway.plugin.http.model.CommandData;
import com.fleetup.gateway.plugin.http.model.HttpRequest;
import com.fleetup.gateway.plugin.http.model.HttpResponse;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.plugin.http.remote.queue.QueueType;
import com.fleetup.gateway.plugin.http.remote.queue.RemoteCommandRedisQueue;
import com.fleetup.gateway.util.MessageSeq;
import com.fleetup.gateway.util.ObjectUtils;
import io.netty.handler.codec.http.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static com.fleetup.gateway.plugin.http.constant.HTTP.COMMAND_PLACEHOLDER;

public class CommandHandler extends HttpHandler {

    private static final Logger log = LoggerFactory.getLogger("remote");

    private QueueService queueService;
    private int timeout;
    private int sequence ;

    public CommandHandler(HttpMethod httpMethod, HttpServer httpServer) {
        super(httpMethod,httpServer);
        queueService = httpServer.getQueueService();
    }

    @Override
    HttpResponse action(HttpRequest req) throws RuntimeException {
        Map<String,String> params = req.getQueryParams();
        log.info("command params: {}",params);
        if(!params.containsKey(ParameterKey.timeout) || !StringUtils.hasValue(params.get(ParameterKey.timeout))){
            timeout = HTTP_TIMEOUT;
        }else {
            timeout = Integer.parseInt(params.get(ParameterKey.timeout).trim());
            if (timeout > 30 || timeout < 5) timeout = HTTP_TIMEOUT;
        }



        boolean cacheCommand = false;
        if (params.containsKey(ParameterKey.offline) && StringUtils.hasValue(params.get(ParameterKey.offline)) && "TRUE".equals(params.get(ParameterKey.offline).trim().toUpperCase())){
            cacheCommand = true;
        }


        if (!params.containsKey(ParameterKey.command) || !StringUtils.hasValue(params.get(ParameterKey.command)))return HttpResponse.BadRequest(ParameterKey.command);
        if (!params.containsKey(ParameterKey.imeis) && !params.containsKey(ParameterKey.imei))return HttpResponse.BadRequest(ParameterKey.imei+" or "+ParameterKey.imeis);
        String command = params.get(ParameterKey.command);
        if(isBatchCommand(params)){
            String[] imeis = params.get(ParameterKey.imeis).split(",");
            return batchCommandHandler(null,command,imeis);
        }else{
            String imei = params.get(ParameterKey.imei);
            if(!params.containsKey(ParameterKey.sequence) || !StringUtils.hasValue(params.get(ParameterKey.sequence))){
                sequence = MessageSeq.getInstance().getNextSeq(imei);
            }else {
                sequence = Integer.parseInt(params.get(ParameterKey.sequence).trim());
                if (sequence > 65535 || timeout < 0) return HttpResponse.BadRequest(ParameterKey.sequence);
            }
            return singleCommandHandler(sequence,command,imei,cacheCommand);
        }
    }


    private boolean isBatchCommand(Map<String,String> params){
        String[] imeis = {};
        if (params.containsKey(ParameterKey.imeis)){
            imeis = params.get(ParameterKey.imeis).split(",");
        }
        return (imeis != null && imeis.length > 0);
    }


    private HttpResponse batchCommandHandler(Integer serialNumber,String command,String... imeis){
        // Start sending instructions to the main server, and start writing the queue after the main server returns the data
        //batch command support offline
        if (imeis != null && imeis.length > 0){
            for (String _imei : imeis) {
                int _serialNum = serialNumber == null ? MessageSeq.getInstance().getNextSeq(_imei) : serialNumber;
                RemoteCommandRedisQueue.build(queueService).withType(QueueType.BATCH).withName(_imei).push(command+COMMAND_PLACEHOLDER+_serialNum);
            }
            return HttpResponse.OK(true,"Command has been pushed");
        }
        return HttpResponse.BadRequest(ParameterKey.imeis);
    }

    private HttpResponse singleCommandHandler(int serialNumber,String command,String imei,boolean cacheCommand){
        if(command.equalsIgnoreCase("clean")){
            if (StringUtils.hasValue(imei)){
                long size = RemoteCommandRedisQueue.build(queueService).withType(QueueType.BATCH).withName(imei).size();
                for (int i = 0; i< size; i++){
                    RemoteCommandRedisQueue.build(queueService).withType(QueueType.BATCH).withName(imei).pop();
                }
                return HttpResponse.OK(true,"Cache directives are cleared. num : "+size);
            }
        }
        //online command
        if (!queueService.isActive(imei)){
            if (cacheCommand){
                return batchCommandHandler(serialNumber,command, imei);
            }else
                return HttpResponse.OK(false,"Device is offline");
        }

        //Start to get the queue data of offline commands, when there are unfinished offline commands, it will show that the device is busy
        if (RemoteCommandRedisQueue.build(queueService).withType(QueueType.BATCH).withName(imei).size() > 0)
            return HttpResponse.OK(false,"The device is busy, Please try again later");

        //When the command comes in, start to judge whether the device is processing the request, if yes, it will show that the device is busy, if not,
        // start the registration request, wait for the request to be completed, and release the request
        if(!queueService.register(imei,timeout)) return HttpResponse.OK(false,"The device is busy, Please try again later");

        //Start pushing remote commands,for multi-instance deployment
        RemoteCommandRedisQueue.build(queueService).withType(QueueType.CHANNEL).push(imei+ COMMAND_PLACEHOLDER+command+COMMAND_PLACEHOLDER+serialNumber);
        //To be processed: The command response does not correspond to the execution time difference of the offline command
        //Obtain the return result of the remote command by blocking the queue
        String rtn = RemoteCommandRedisQueue.build(queueService).withName(imei).pop(timeout);
        queueService.release(imei); // release the request
        return HttpResponse.OK(StringUtils.hasValue(rtn),StringUtils.hasValue(rtn) ? ObjectUtils.parseObject(rtn, CommandData.class) : "Device not responding");


    }

}
