package com.fleetup.gateway.plugin.http.handler;

import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.plugin.http.HttpServer;
import com.fleetup.gateway.plugin.http.model.HttpRequest;
import com.fleetup.gateway.plugin.http.model.HttpResponse;
import com.fleetup.gateway.plugin.http.remote.QueueService;
import com.fleetup.gateway.server.Version;
import io.netty.handler.codec.http.HttpMethod;

import java.io.File;
import java.nio.file.Files;
import java.util.*;

public class StreamHandler extends HttpHandler{


    private static final String URL_STREAM = "/stream";


    private QueueService queueService;
    public StreamHandler(HttpMethod httpMethod, HttpServer httpServer) {
        super(httpMethod, httpServer);
        queueService = httpServer.getQueueService();
    }

    @Override
    HttpResponse action(HttpRequest req) throws Exception {
        Map<String,String> params = req.getQueryParams();
        log.debug("params : {}",params);
        if(req.getUrl().contains(URL_STREAM)){
            if (!params.containsKey(ParameterKey.imei))return HttpResponse.BadRequest(ParameterKey.imei);







        }
        return null;
    }
}
