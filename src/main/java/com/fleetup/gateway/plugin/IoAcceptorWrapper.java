package com.fleetup.gateway.plugin;


import io.netty.bootstrap.ServerBootstrap;

/**
 * @className: IoAcceptorWrapper
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class IoAcceptorWrapper {

    private ServerBootstrap ioAcceptor;

    public IoAcceptorWrapper() {
        super();
    }

    public IoAcceptorWrapper(ServerBootstrap ioAcceptor) {
        super();
        this.ioAcceptor = ioAcceptor;
    }

    public ServerBootstrap getIoAcceptor() {
        return ioAcceptor;
    }

    public void setIoAcceptor(ServerBootstrap ioAcceptor) {
        this.ioAcceptor = ioAcceptor;
    }
}
