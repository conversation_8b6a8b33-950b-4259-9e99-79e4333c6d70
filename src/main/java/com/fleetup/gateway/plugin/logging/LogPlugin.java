package com.fleetup.gateway.plugin.logging;


import com.alibaba.fastjson.JSON;
import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.IDataPublisher;
import com.fleetup.gateway.plugin.IDataPublisherCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.Properties;

/**
 * @className: LogPlugin
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class LogPlugin implements IDataPublisher {
    private static final Logger LOG = LoggerFactory.getLogger("event");
    private static final boolean prettyPrint = false;


    @Override
    public boolean start(Properties properties) {
        Config config = new Config(properties);
        if (config.getValueAsBoolean("gateway.pub.logging.enable", true)) {
            LOG.info("[{}] started", getClass().getSimpleName());
            return true;
        } else {
            LOG.info("[{}] disabled", getClass().getSimpleName());
            return false;
        }
    }

    public <T> void print(EventMessage<T> eventMessage){
        LOG.debug("{}", JSON.toJSONString(eventMessage, prettyPrint));
    }

    public <T> void print(List<EventMessage<T>> eventMessages){
        LOG.debug("{}", JSON.toJSONString(eventMessages, prettyPrint));
    }

    public void publishRecord(String record) {
        LOG.debug("{}", record);
    }

    @Override
    public <T> void publishRecord(EventMessage<T> eventMessage) {
        this.publishRecord(JSON.toJSONString(eventMessage, prettyPrint));
    }

    public void publishStringRecords(Collection<String> records) {
        for (String record : records) {
            this.publishRecord(record);
        }
    }

    public <T> void publishRecords(Collection<EventMessage<T>> records) {
        for (EventMessage<T> record : records) {
            this.publishRecord(record);
        }
    }

    @Override
    public boolean stop() {
        LOG.info("[{}] stopped", getClass().getSimpleName());
        return true;
    }

    public boolean enabled() {
        return false;
    }

    @Override
    public IDataPublisherCallback getCallback() {
        return null;
    }
}

