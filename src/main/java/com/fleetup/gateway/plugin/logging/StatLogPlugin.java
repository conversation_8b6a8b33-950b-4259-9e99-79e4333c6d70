package com.fleetup.gateway.plugin.logging;


import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.IDataPublisher;
import com.fleetup.gateway.plugin.IDataPublisherCallback;
import com.fleetup.gateway.server.IServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * @className: StatLogPlugin
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class StatLogPlugin implements IDataPublisher {
    private static final Logger LOG = LoggerFactory.getLogger("stat");
    private IServer mainServer;
    private long statInterval = 30*1000L;
    private long currentTimeInMillis = 0;

    @Override
    public boolean start(Properties properties) {
        Config config = new Config(properties);
        if (config.getValueAsBoolean("gateway.pub.stat.enable", true)) {
            LOG.info("[{}] started", getClass().getSimpleName());
            return true;
        } else {
            LOG.info("[{}] disabled", getClass().getSimpleName());
            return false;
        }
    }

    @Override
    public <T> void publishRecord(EventMessage<T> eventMessage) {
        if (mainServer.getChannels() == null) {
            return ;
        }
        if (System.currentTimeMillis() - currentTimeInMillis > statInterval) {
            currentTimeInMillis = System.currentTimeMillis();
            LOG.info("Online Count: {}", mainServer.getChannels().size());
        }
    }

    @Override
    public boolean stop() {
        LOG.info("[{}] stopped", getClass().getSimpleName());
        return true;
    }

    @Override
    public IDataPublisherCallback getCallback() {
        return server -> {
            LOG.info("IDataPublisherCallback: {}", server);
            mainServer = server;
        };
    }
}

