package com.fleetup.gateway.plugin.kinesis;


import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.util.JsonUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisClient;
import software.amazon.awssdk.services.kinesis.model.PutRecordsRequest;
import software.amazon.awssdk.services.kinesis.model.PutRecordsRequestEntry;
import software.amazon.awssdk.services.kinesis.model.PutRecordsResponse;
import software.amazon.awssdk.services.kinesis.model.PutRecordsResultEntry;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @className: KinesisProducer
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class KinesisProducer {
    private static final Logger logger = LoggerFactory.getLogger(KinesisProducer.class);

    private static final String DEFAULT_STREAM = "stress-gw-3vl-stream";
    private static final int DEFAULT_MAX_RECORDS_SIZE = 100;
    private static final int DEFAULT_MAX_RETRY = 3;
    private static final int DEFAULT_PUSH_INTERVAL = 100;

    private final KinesisClient kinesisClient;

    private final int maxDataSize;
    private final int maxRetry;
    private final String kinesisStreamName;

    private final int pushInterval;

    /**
     * same with c++ producer, every record is a json string
     * one producer event ==> one kinesis record
     */
    private static final Queue<String> EVENT_MESSAGE_QUEUE = new ConcurrentLinkedQueue<>();

    public KinesisProducer(Config config) {
        kinesisClient = KinesisClient.builder()
                .region(Region.US_WEST_2)
                .build();
        kinesisStreamName = config.getValue("gateway.pub.kinesis.stream.name",DEFAULT_STREAM);
        maxDataSize = config.getValueAsInt("gateway.pub.kinesis.batch.data.size",DEFAULT_MAX_RECORDS_SIZE);
        maxRetry = config.getValueAsInt("gateway.pub.kinesis.retry.times",DEFAULT_MAX_RETRY);
        pushInterval = config.getValueAsInt("gateway.pub.kinesis.check.interval",DEFAULT_PUSH_INTERVAL);
    }

    public <T> void addQueue(EventMessage<T> eventMessage) {
        try {
            // C++ gw producer use json string as a kinesis record
            String eventStr = JsonUtils.writeValueAsString(eventMessage);
            if (eventStr != null) {
                EVENT_MESSAGE_QUEUE.add(eventStr);
            } else {
                logger.error("Failed to serialize EventMessage to JSON string.");
            }
        } catch (Exception e) {
            logger.error("Push data to queue fail, ", e);
        }
    }

    public void processQueue() {
        int size = EVENT_MESSAGE_QUEUE.size();
        if (size == 0) return;
        int loopCnt = size % maxDataSize == 0 ? size/ maxDataSize : size/ maxDataSize + 1;

        List<PutRecordsRequestEntry> putRecordsRequestEntryList = new ArrayList<>();

        PutRecordsRequest request;

        for (int i = 1; i <= loopCnt; i++) {
            logger.debug("[KINESIS] Chunk-{}, kinesisRequestQueueSize: {}, RemainingQueueSize:  {}", i, size, EVENT_MESSAGE_QUEUE.size());

            int byteSize = 0;

            for (int j = 0; j< maxDataSize && !EVENT_MESSAGE_QUEUE.isEmpty(); j++) {
                String poll = EVENT_MESSAGE_QUEUE.poll();
                byte[] data = poll.getBytes(StandardCharsets.UTF_8);

                byteSize = byteSize + data.length;
                PutRecordsRequestEntry entry = PutRecordsRequestEntry.builder()
                        // pk << "pk-" << (::rand()) in C++ Gateway
                        .partitionKey(RandomStringUtils.random(10, true, true))
                        .data(SdkBytes.fromByteArray(data))
                        .build();
                putRecordsRequestEntryList.add(entry);
            }

            if (!putRecordsRequestEntryList.isEmpty()) {
                request = PutRecordsRequest.builder()
                        .streamName(kinesisStreamName)
                        .records(putRecordsRequestEntryList)
                        .build();

                PutRecordsResponse response = kinesisClient.putRecords(request);

                if (response.failedRecordCount() == 0) {
                    logger.debug("[KINESIS] PutRecordsResult: Success, {} records({} bytes) were sent.", putRecordsRequestEntryList.size(), byteSize);
                    putRecordsRequestEntryList.clear();
                } else {
                    // fail
                    retry(putRecordsRequestEntryList, response, 1);
                }
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                logger.warn("processQueue() sleep interrupted..., ", e);
                Thread.currentThread().interrupt();
            }

            logger.debug( "[KINESIS] {} Chunk done, ChunkSize: {}, Leave Queue Size: {}", i, maxDataSize, EVENT_MESSAGE_QUEUE.size());
        }
    }

    private void retry(List<PutRecordsRequestEntry> putRecordsRequestEntryList, PutRecordsResponse response, int retryTimes) {
        if (retryTimes > maxRetry) {
            logger.error("Push data to kinesis fail, max retry.");
            return;
        }
        List<PutRecordsRequestEntry> failedRecordsList = new ArrayList<>();
        List<PutRecordsResultEntry> putRecordsResultEntryList = response.records();

        for (int i=0;i<putRecordsResultEntryList.size();i++) {
            PutRecordsRequestEntry putRecordsRequestEntry = putRecordsRequestEntryList.get(i);
            PutRecordsResultEntry putRecordsResultEntry = putRecordsResultEntryList.get(i);
            if (putRecordsResultEntry.errorCode().length()>0) {
                failedRecordsList.add(putRecordsRequestEntry);
            }
        }
        PutRecordsRequest request = PutRecordsRequest.builder()
                .streamName(kinesisStreamName)
                .records(failedRecordsList)
                .build();

        response = kinesisClient.putRecords(request);
        if (response.failedRecordCount() == 0) {
            return;
        }

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            logger.warn("retry() sleep interrupted..., ", e);
            Thread.currentThread().interrupt();
        }
        retry(failedRecordsList, response, retryTimes + 1);
    }

    public int getPushInterval() {
        return pushInterval;
    }
}

