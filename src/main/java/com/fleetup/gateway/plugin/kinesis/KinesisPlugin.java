package com.fleetup.gateway.plugin.kinesis;


import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;
import com.fleetup.gateway.plugin.IDataPublisher;
import com.fleetup.gateway.plugin.IDataPublisherCallback;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * @className: KinesisPlugin
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class KinesisPlugin implements IDataPublisher {

    private static final Logger LOG = LoggerFactory.getLogger(KinesisPlugin.class);

    private KinesisProcessorContext processorContext;

    @Override
    public <T> void publishRecord(EventMessage<T> eventMessage) {
        if (!StringUtils.isEmpty(eventMessage.getDevId())){
            processorContext.addQueue(eventMessage);
        } else {
            LOG.warn("devId: [{}] protocolId: [{}] Because the data lacks devId, this data is skipped and will not be pushed to Kinesis",eventMessage.getDevId(),eventMessage.getProtocolId());
        }
    }

    @Override
    public IDataPublisherCallback getCallback() {
        return null;
    }

    @Override
    public boolean start(Properties properties) {
        Config config = new Config(properties);
        if (config.getValueAsBoolean("gateway.pub.kinesis.enable", true)) {
            processorContext = new KinesisProcessorContext(config);

            LOG.info("[{}] started", getClass().getSimpleName());
            return true;
        } else {
            LOG.info("[{}] disabled", getClass().getSimpleName());
            return false;
        }
    }

    @Override
    public boolean stop() {
        KinesisProducer producer = processorContext.getKinesisProducer();
        if (producer != null){
            producer.processQueue();
        }
        LOG.info("[{}] stopped", getClass().getSimpleName());
        return true;
    }

}
