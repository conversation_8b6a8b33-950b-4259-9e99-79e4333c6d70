package com.fleetup.gateway.plugin.kinesis;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: KinesisProcessorWorker
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class KinesisProcessorWorker {

    private static final Logger LOG = LoggerFactory.getLogger(KinesisProcessorWorker.class);

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactory() {
        private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = defaultFactory.newThread(r);
            thread.setName("kinesisProcessWorker-" + threadNumber.getAndIncrement());
            return thread;
        }
    };

    private KinesisProducer producer;

    public KinesisProcessorWorker(KinesisProducer producer) {
        this.producer = producer;
    }

    public void start() {
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1, THREAD_FACTORY);
        executor.scheduleWithFixedDelay(new Thread("KinesisProcessorQueue") {
            @Override
            public void run() {
                try {
                    long start = System.currentTimeMillis();
                    producer.processQueue();
                    long duration = System.currentTimeMillis()-start;
                    if (duration > 100) {
                        LOG.warn("Process time[KinesisProcessorQueue]: {}ms", duration);
                    }
                } catch (Exception e) {
                    LOG.error("Batch process failed", e);
                }
            }
        }, 1000, producer.getPushInterval(), TimeUnit.MILLISECONDS);
    }
}

