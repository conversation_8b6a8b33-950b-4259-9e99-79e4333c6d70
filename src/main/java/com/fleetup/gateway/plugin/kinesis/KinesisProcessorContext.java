package com.fleetup.gateway.plugin.kinesis;


import com.fleetup.gateway.event.EventMessage;
import com.fleetup.gateway.plugin.Config;

/**
 * @className: KinesisProcessorContext
 * @author: Justin
 * @Date: 2024-05-22
 **/
public class KinesisProcessorContext {
    private KinesisProcessorWorker kinesisProcessWorker;

    private KinesisProducer kinesisProducer;

    public KinesisProcessorContext(Config config) {
        kinesisProducer = new KinesisProducer(config);
        kinesisProcessWorker = new KinesisProcessorWorker(kinesisProducer);
        kinesisProcessWorker.start();
    }

    public KinesisProducer getKinesisProducer(){
        if (kinesisProducer != null){
            return kinesisProducer;
        }
        return null;
    }

    public <T> void addQueue(EventMessage<T> eventMessage) {
        kinesisProducer.addQueue(eventMessage);
    }
}
