package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * 0X0100
 * @className: RegisterMO
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class RegisterMO extends BaseStructure {

    private U16 manufacturer;
    private U16 authenticateLevel;
    private StrF deviceType;
    private StrF iCCID;
    private StrF deviceSN;
    private U8 vehiclePlateColor;
    private String vehicleIdentification;

    public RegisterMO(byte[] message){
        super(message);
        this.setManufacturer(U16.valueOf(nextBytes(2)));
        this.setAuthenticateLevel(U16.valueOf(nextBytes(2)));
        this.setDeviceType(StrF.valueOf(nextBytes(5)));
        this.setiCCID(StrF.valueOf(nextBytes(20)));
        this.setDeviceSN(StrF.valueOf(nextBytes(7)));
        this.setVehiclePlateColor(U8.valueOf(nextByte()));

    }

    public U16 getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(U16 manufacturer) {
        this.manufacturer = manufacturer;
    }

    public U16 getAuthenticateLevel() {
        return authenticateLevel;
    }

    public void setAuthenticateLevel(U16 authenticateLevel) {
        this.authenticateLevel = authenticateLevel;
    }

    public StrF getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(StrF deviceType) {
        this.deviceType = deviceType;
    }

    public StrF getiCCID() {
        return iCCID;
    }

    public void setiCCID(StrF iCCID) {
        this.iCCID = iCCID;
    }

    public StrF getDeviceSN() {
        return deviceSN;
    }

    public void setDeviceSN(StrF deviceSN) {
        this.deviceSN = deviceSN;
    }

    public U8 getVehiclePlateColor() {
        return vehiclePlateColor;
    }

    public void setVehiclePlateColor(U8 vehiclePlateColor) {
        this.vehiclePlateColor = vehiclePlateColor;
    }


    public String getVehicleIdentification() {
        return vehicleIdentification;
    }

    public void setVehicleIdentification(String vehicleIdentification) {
        this.vehicleIdentification = vehicleIdentification;
    }
}
