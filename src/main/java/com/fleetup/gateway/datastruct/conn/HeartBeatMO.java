package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.google.common.base.MoreObjects;

/**
 * 0x0002
 * @className: HeartBeatMO
 * @author: <PERSON>
 * @Date: 2024-05-17
 **/
public class HeartBeatMO extends BaseStructure {

    public HeartBeatMO() {
        super(new byte[]{});
    }

    public HeartBeatMO(byte[] message) {
        super(message);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass()).toString();
    }
}
