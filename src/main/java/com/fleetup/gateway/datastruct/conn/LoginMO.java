package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;

/**
 * 0x0102
 * @className: LoginMO
 * @author: <PERSON>
 * @Date: 2024-05-17
 **/
public class LoginMO extends BaseStructure {

    private StrF baseKey;

    public LoginMO(byte[] message){
        super(message);
        this.setBaseKey(StrF.valueOf(message));
    }

    public StrF getBaseKey() {
        return baseKey;
    }

    public void setBaseKey(StrF baseKey) {
        this.baseKey = baseKey;
    }
}
