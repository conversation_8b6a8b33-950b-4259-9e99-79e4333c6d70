package com.fleetup.gateway.datastruct.conn;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;
import com.fleetup.gateway.util.Hex;

/**
 * 0X8100
 * @className: RegisterMT
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class RegisterMT extends BaseStructure {

    private U16 respSeqNumber;
    private U8 result;
    private StrF baseKey;

    public RegisterMT(byte[] message){
        super(message);
        this.respSeqNumber = U16.valueOf(nextBytes(2));
        this.result = U8.valueOf(nextByte());
        this.baseKey = StrF.valueOf(nextBytes(message.length - 3));
    }

    public RegisterMT(int respSeqNumber,int result,String baseKey){
        this.setRespSeqNumber(new U16(respSeqNumber));
        this.setResult(new U8(result));
        this.setBaseKey(new StrF(Hex.convertStrToAscii(baseKey)));
        this.setMessage(GatewayUtils.addAll(this.respSeqNumber.toBytes(),this.result.toBytes(),this.baseKey.getMessage()));
    }

    public U16 getRespSeqNumber() {
        return respSeqNumber;
    }

    public void setRespSeqNumber(U16 respSeqNumber) {
        this.respSeqNumber = respSeqNumber;
    }

    public U8 getResult() {
        return result;
    }

    public void setResult(U8 result) {
        this.result = result;
    }

    public StrF getBaseKey() {
        return baseKey;
    }

    public void setBaseKey(StrF baseKey) {
        this.baseKey = baseKey;
    }
}

