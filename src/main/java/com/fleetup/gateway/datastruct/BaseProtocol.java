package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.datastruct.basic.BCD;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.CrcUtils;
import com.fleetup.gateway.util.EscapeUtils;
import com.fleetup.gateway.util.GatewayUtils;
import com.fleetup.gateway.util.Hex;

import java.math.BigInteger;

/**
 * @className: BaseProtocol
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class BaseProtocol extends BaseStructure {
    public static final int NOT_ENCRYPTED = 0x00;
    public static final int RSA_ENCRYPTED = 0x01;

    private U8 protocolHead;
    private U16 messageId;
    private byte[] messageBodyAttributes;
    private transient int messageBodyLength;
    private transient int encryption;
    private transient boolean segment;
    private BCD deviceId;
    private U16 serialNumber;
    private BaseStructure payload;
    private U8 crc;
    private U8 protocolTail;

    public BaseProtocol(byte[] message) {
        super(message);
        this.protocolHead = U8.valueOf(nextByte());
        this.messageId = U16.valueOf(nextBytes(2));
        this.messageBodyAttributes = nextBytes(2);
        this.messageBodyLength = ((messageBodyAttributes[0] & 0x03) << 8) + (messageBodyAttributes[1] & 0xFF);
        this.encryption = (messageBodyAttributes[0] & 0x1c) == NOT_ENCRYPTED ? NOT_ENCRYPTED : RSA_ENCRYPTED;
        this.segment = (messageBodyAttributes[0] & 0x20) == 0x20;
        this.deviceId = BCD.valueOf(nextBytes(6));
        this.serialNumber = U16.valueOf(nextBytes(2));
        this.payload = new BaseStructure(nextBytes(messageBodyLength));
        this.crc = U8.valueOf(message[message.length - 2]);
        this.protocolTail = U8.valueOf(message[message.length - 1]);
    }

    public BaseProtocol(String deviceId, int protocolId, BaseStructure payload,int serialNumber) {
        BigInteger decimalValue = new BigInteger(deviceId);
        this.protocolHead = new U8(0x7e);
        this.messageId = new U16(protocolId);
        U16 messageBodyProperties = new U16(payload.getLength());
        this.deviceId = new BCD(Hex.fromHexString(decimalValue.toString(16)));
        this.serialNumber = new U16(serialNumber);
        this.payload = payload;
        byte[] psrc = GatewayUtils.addAll(protocolHead.toBytes(),messageId.toBytes(),
                messageBodyProperties.toBytes(), this.deviceId.toBytes(), this.serialNumber.toBytes(), this.payload.toBytes());
        this.crc = new U8(CrcUtils.getCheckCode(psrc));
        this.protocolTail = new U8(0x7e);
        this.setMessage(EscapeUtils.encodeMessage(GatewayUtils.addAll(psrc,this.crc.toBytes(),this.protocolTail.toBytes())));
    }

    public U16 getMessageId() {
        return messageId;
    }

    public void setMessageId(U16 messageId) {
        this.messageId = messageId;
    }

    public byte[] getMessageBodyAttributes() {
        return messageBodyAttributes;
    }

    public void setMessageBodyAttributes(byte[] messageBodyAttributes) {
        this.messageBodyAttributes = messageBodyAttributes;
    }

    public int getMessageBodyLength() {
        return messageBodyLength;
    }

    public void setMessageBodyLength(int messageBodyLength) {
        this.messageBodyLength = messageBodyLength;
    }

    public int getEncryption() {
        return encryption;
    }

    public void setEncryption(int encryption) {
        this.encryption = encryption;
    }

    public boolean isSegment() {
        return segment;
    }

    public void setSegment(boolean segment) {
        this.segment = segment;
    }

    public BCD getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(BCD deviceId) {
        this.deviceId = deviceId;
    }

    public U16 getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(U16 serialNumber) {
        this.serialNumber = serialNumber;
    }

    public BaseStructure getPayload() {
        return payload;
    }

    public void setPayload(BaseStructure payload) {
        this.payload = payload;
    }

    public U8 getCrc() {
        return crc;
    }

    public void setCrc(U8 crc) {
        this.crc = crc;
    }

    public U8 getProtocolHead() {
        return protocolHead;
    }

    public void setProtocolHead(U8 protocolHead) {
        this.protocolHead = protocolHead;
    }

    public U8 getProtocolTail() {
        return protocolTail;
    }

    public void setProtocolTail(U8 protocolTail) {
        this.protocolTail = protocolTail;
    }
}
