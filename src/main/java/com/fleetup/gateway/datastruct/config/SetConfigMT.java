package com.fleetup.gateway.datastruct.config;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: SetConfigMO
 * @author: Justin
 * @Date: 2024-06-07
 **/
public class SetConfigMT extends BaseStructure {

    private U8 tlvCount;
    private TLV[] tlvArray;

    public SetConfigMT(byte[] message){
        super(message);
        this.tlvCount = U8.valueOf(nextByte());
        this.tlvArray = new TLV[tlvCount.getValue()];
        for (int i = 0; i < tlvArray.length; i++) {
            U16 tag = U16.valueOf(nextBytes(2));
            U8 length = U8.valueOf(nextByte());
            byte[] valueArray;
            if (length.getValue() == 0){
                valueArray = new byte[]{};
            } else {
                valueArray = nextBytes(length.getValue());
            }
            tlvArray[i] = new TLV(tag,length,valueArray);
        }
    }

    public U8 getTlvCount() {
        return tlvCount;
    }

    public void setTlvCount(U8 tlvCount) {
        this.tlvCount = tlvCount;
    }

    public TLV[] getTlvArray() {
        return tlvArray;
    }

    public void setTlvArray(TLV[] tlvArray) {
        this.tlvArray = tlvArray;
    }
}
