package com.fleetup.gateway.datastruct.config;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;
import com.google.common.base.MoreObjects;

/**
 * @className: QueryConfigMT
 * @author: Justin
 * @Date: 2024-06-07
 **/
public class QueryConfigMT extends BaseStructure {

    private U8 tlvCount;
    private U16[] tlvArray;

    public QueryConfigMT(byte[] message) {
        super(message);
        this.tlvCount = U8.valueOf(nextByte());
        if (this.tlvCount.getValue() > 0) {
            tlvArray = new U16[this.tlvCount.getValue()];
            for (int i = 0; i < tlvArray.length; i++) {
                tlvArray[i] = U16.valueOf(nextBytes(2));
            }
        } else {
            tlvArray = new U16[]{};
        }
    }

    public QueryConfigMT(int tlv) {
        this(new int[]{tlv});
    }

    public QueryConfigMT(int[] tlvTags) {
        setTlvCount(U8.valueOf(tlvTags.length));
        this.tlvArray = new U16[tlvTags.length];
        for (int i = 0; i < this.tlvArray.length; i++) {
            this.tlvArray[i] = U16.valueOf(tlvTags[i]);
        }
        this.setMessage(GatewayUtils.addAll(new byte[]{getTlvCount().toByte()}, GatewayUtils.getBytes(tlvArray)));
    }

    public void setTlvCount(U8 tlvCount) {
        this.tlvCount = tlvCount;
    }
    public void setTlvArray(U16[] tlvArray) {
        this.tlvArray = tlvArray;
    }
    public U8 getTlvCount() {
        return tlvCount;
    }
    public U16[] getTlvArray() {
        return tlvArray;
    }
    public String[] getTlvArrayAsHex() {
        String[] arr = new String[tlvArray.length];
        for (int i = 0; i < arr.length; i++) {
            arr[i] = tlvArray[i].toHexString();
        }
        return arr;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("tlv_count", tlvCount)
                .add("tlv_array(hex)", getTlvArrayAsHex())
                .toString();
    }


}
