package com.fleetup.gateway.datastruct.config;

import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;

public class CommandRequest extends BaseStructure {

    private U8 flag;
    private String commandContent;


    public CommandRequest(byte[] message){
        super(message);
        this.flag = readU8();
        this.commandContent =  new String(readRestBytes());
    }

    public CommandRequest(String commandContent){
        this(GatewayUtils.addAll(U8.valueOf(0).toBytes(),commandContent.getBytes()));
    }


    public U8 getFlag() {
        return flag;
    }

    public void setFlag(U8 flag) {
        this.flag = flag;
    }

    public String getCommandContent() {
        return commandContent;
    }

    public void setCommandContent(String commandContent) {
        this.commandContent = commandContent;
    }
}
