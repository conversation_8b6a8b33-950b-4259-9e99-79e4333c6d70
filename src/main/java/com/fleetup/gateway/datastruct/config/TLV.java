package com.fleetup.gateway.datastruct.config;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;

/**
 * @className: TLV
 * @author: Justin
 * @Date: 2024-06-07
 **/
public class TLV extends BaseStructure {

    private U16 tag;
    private U8 dataLength;
    private byte[] valueArray;

    public TLV(byte[] message) {
        super(message);
        this.tag = U16.valueOf(nextByte());
        this.dataLength = U8.valueOf(nextByte());
        this.valueArray = nextBytes(dataLength.getValue());
    }

    public TLV(U16 dataId, U8 dataLength, byte[] valueArray) {
        this.tag = dataId;
        this.dataLength = dataLength;
        this.valueArray = valueArray;
        setMessage(GatewayUtils.addAll(dataId.toBytes(), dataLength.toBytes(), valueArray));
    }

    public U16 getTag() {
        return tag;
    }

    public void setTag(U16 tag) {
        this.tag = tag;
    }

    public U8 getDataLength() {
        return dataLength;
    }

    public void setDataLength(U8 dataLength) {
        this.dataLength = dataLength;
    }

    public byte[] getValueArray() {
        return valueArray;
    }

    public void setValueArray(byte[] valueArray) {
        this.valueArray = valueArray;
    }
}
