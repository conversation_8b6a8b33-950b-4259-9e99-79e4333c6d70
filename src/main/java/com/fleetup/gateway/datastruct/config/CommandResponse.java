package com.fleetup.gateway.datastruct.config;

import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;

public class CommandResponse extends BaseStructure {

    private U16 sequenceNum;
    private U8 encoding;
    private String commandContent;


    public CommandResponse(byte[] message){
        super(message);
        this.sequenceNum = readU16();
        this.encoding = readU8();
        this.commandContent = new String(readRestBytes());
    }

    public U16 getSequenceNum() {
        return sequenceNum;
    }

    public void setSequenceNum(U16 sequenceNum) {
        this.sequenceNum = sequenceNum;
    }

    public String getCommandContent() {
        return commandContent;
    }

    public void setCommandContent(String commandContent) {
        this.commandContent = commandContent;
    }

    public U8 getEncoding() {
        return encoding;
    }

    public void setEncoding(U8 encoding) {
        this.encoding = encoding;
    }
}
