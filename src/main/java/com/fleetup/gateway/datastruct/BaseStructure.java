package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.datastruct.basic.BCD;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.Hex;

/**
 * @className: BaseStructure
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class BaseStructure {
    private transient byte[] message;

    public BaseStructure() {
    }

    /**
     * pos start with -1
     */
    private int pos = -1;

    public BaseStructure(byte[] message) {
        this.message = message;
    }

    /**
     *
     * @param start start index(include)
     * @param end end index(include)
     * @return
     */
    public byte[] getByteField(int start, int end) {
        byte[] bytes = new byte[end - start + 1];
        for (int i = 0; start + i <= end; i++) {
            bytes[i] = message[start + i];
        }
        return bytes;
    }

    public byte getByteField(int pos) {
        return message[pos];
    }


    public byte nextByte() {
        pos++;
        return message[pos];
    }

    public byte[] nextBytes(int size) {
        byte[] bytes = new byte[size];
        for (int i = 0; i < size; i++) {
            bytes[i] = message[++pos];
        }
        return bytes;
    }

    public int getPos(){
        return pos;
    }
    public int skipPos(int skip){
        pos += skip;
        return pos;
    }
    public int backPos(int back){
        pos -= back;
        return pos;
    }

    public byte[] readRestBytes() {
        return nextBytes(getLength() - 1 - pos);
    }

    public U8 readU8() {
        return U8.valueOf(nextByte());
    }

    public U16 readU16() {
        return U16.valueOf(nextBytes(2));
    }

    public U32 readU32() {
        return U32.valueOf(nextBytes(4));
    }

    public BCD readBCD(int size) {
        return BCD.valueOf(nextBytes(size));
    }

    public int getLength(){
        return message.length;
    }

    public byte[] getMessage(){
        return message;
    }
    public void setMessage(byte[] message) {
        this.message = message;
    }

    public String getHexString(int start, int end){
        return Hex.toHexString(this.getByteField(start, end));
    }
    public String getHexString(int pos){
        return Hex.toHexString(this.getByteField(pos));
    }
    public String getHexString(byte... data){
        return Hex.toHexString(data);
    }

    public byte[] toBytes(){
        return message;
    }

    public String toHexString() {
        return Hex.toHexString(message);
    }

    @Override
    public String toString() {
        return Hex.toHexString(message);
    }
}
