package com.fleetup.gateway.datastruct.transmission;

import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.google.common.base.MoreObjects;

public class DTC extends BaseStructure {

    private U16 reserve;
    private U16 code;
    private U16 status;
    private U16 carType;
    private U16 decodeLength;
    private byte[] codeDecode;
    private U8 decodeStatus;


    public DTC(byte[] message){
        super(message);
        this.reserve = U16.valueOf(nextBytes(2));
        this.code = U16.valueOf(nextBytes(2));
        this.status = U16.valueOf(nextBytes(2));
        this.carType = U16.valueOf(nextBytes(2));
        this.decodeLength = U16.valueOf(nextBytes(2));
        this.codeDecode = nextBytes(5);
        this.decodeStatus = U8.valueOf(nextByte());
    }

    public U16 getReserve() {
        return reserve;
    }

    public void setReserve(U16 reserve) {
        this.reserve = reserve;
    }

    public U16 getCode() {
        return code;
    }

    public void setCode(U16 code) {
        this.code = code;
    }

    public U16 getStatus() {
        return status;
    }

    public void setStatus(U16 status) {
        this.status = status;
    }

    public U16 getCarType() {
        return carType;
    }

    public void setCarType(U16 carType) {
        this.carType = carType;
    }

    public U16 getDecodeLength() {
        return decodeLength;
    }

    public void setDecodeLength(U16 decodeLength) {
        this.decodeLength = decodeLength;
    }

    public byte[] getCodeDecode() {
        return codeDecode;
    }

    public void setCodeDecode(byte[] codeDecode) {
        this.codeDecode = codeDecode;
    }

    public U8 getDecodeStatus() {
        return decodeStatus;
    }

    public void setDecodeStatus(U8 decodeStatus) {
        this.decodeStatus = decodeStatus;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("reserve", reserve)
                .add("code", code)
                .add("status", status)
                .add("carType", carType)
                .add("decodeLength", decodeLength)
                .add("codeDecode", codeDecode)
                .add("codeDecodeStr", new String(codeDecode))
                .add("decodeStatus", decodeStatus)
                .toString();
    }


}
