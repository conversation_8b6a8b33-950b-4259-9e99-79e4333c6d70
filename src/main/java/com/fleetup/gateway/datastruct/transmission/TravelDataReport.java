package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.BCD;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: TravelDataReport
 * @author: Justin
 * @Date: 2024-05-29
 **/
public class TravelDataReport extends BaseStructure {

    private U8 travelProperty;
    private U32 travelNumber;
    private BCD startTime;
    private BCD endTime;
    private U32 startLatitude;
    private U32 startLongitude;
    private U32 endLatitude;
    private U32 endLongitude;
    private U8 locationStatus;
    private U16 idlingCount;
    private U16 idlingTime;
    private U16 tripMileage;
    private U16 tripFuelConsumption;

    public TravelDataReport(byte[] message){
        super(message);
        this.travelProperty = U8.valueOf(nextByte());
        this.travelNumber = U32.valueOf(nextBytes(4));
        this.startTime = BCD.valueOf(nextBytes(6));
        if (travelProperty.getValue() == 2){
            this.endTime = BCD.valueOf(nextBytes(6));
            this.startLatitude = U32.valueOf(nextBytes(4));
            this.startLongitude = U32.valueOf(nextBytes(4));
            this.endLatitude = U32.valueOf(nextBytes(4));
            this.endLongitude = U32.valueOf(nextBytes(4));
            this.locationStatus = U8.valueOf(nextByte());
            this.idlingCount = U16.valueOf(nextBytes(2));
            this.idlingTime = U16.valueOf(nextBytes(2));
            this.tripMileage = U16.valueOf(nextBytes(2));
            this.tripFuelConsumption = U16.valueOf(nextBytes(2));
        }
    }

    public U8 getTravelProperty() {
        return travelProperty;
    }

    public void setTravelProperty(U8 travelProperty) {
        this.travelProperty = travelProperty;
    }

    public U32 getTravelNumber() {
        return travelNumber;
    }

    public void setTravelNumber(U32 travelNumber) {
        this.travelNumber = travelNumber;
    }

    public BCD getStartTime() {
        return startTime;
    }

    public void setStartTime(BCD startTime) {
        this.startTime = startTime;
    }

    public BCD getEndTime() {
        return endTime;
    }

    public void setEndTime(BCD endTime) {
        this.endTime = endTime;
    }

    public U32 getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(U32 startLatitude) {
        this.startLatitude = startLatitude;
    }

    public U32 getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(U32 startLongitude) {
        this.startLongitude = startLongitude;
    }

    public U32 getEndLatitude() {
        return endLatitude;
    }

    public void setEndLatitude(U32 endLatitude) {
        this.endLatitude = endLatitude;
    }

    public U32 getEndLongitude() {
        return endLongitude;
    }

    public void setEndLongitude(U32 endLongitude) {
        this.endLongitude = endLongitude;
    }

    public U8 getLocationStatus() {
        return locationStatus;
    }

    public void setLocationStatus(U8 locationStatus) {
        this.locationStatus = locationStatus;
    }

    public U16 getIdlingCount() {
        return idlingCount;
    }

    public void setIdlingCount(U16 idlingCount) {
        this.idlingCount = idlingCount;
    }

    public U16 getIdlingTime() {
        return idlingTime;
    }

    public void setIdlingTime(U16 idlingTime) {
        this.idlingTime = idlingTime;
    }

    public U16 getTripMileage() {
        return tripMileage;
    }

    public void setTripMileage(U16 tripMileage) {
        this.tripMileage = tripMileage;
    }

    public U16 getTripFuelConsumption() {
        return tripFuelConsumption;
    }

    public void setTripFuelConsumption(U16 tripFuelConsumption) {
        this.tripFuelConsumption = tripFuelConsumption;
    }
}
