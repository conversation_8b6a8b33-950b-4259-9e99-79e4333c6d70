package com.fleetup.gateway.datastruct.transmission;

import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.util.GatewayUtils;

import java.util.Arrays;
import java.util.stream.IntStream;

public class DTCData extends BaseStructure {
    private U32 systemId;
    private U16 troubleCount;
    private DTC[] troubleCodes;

     public DTCData(byte[] message){
         super(message);
         this.systemId = U32.valueOf(nextBytes(4));
         this.troubleCount = U16.valueOf(nextBytes(2));
         byte[] original = nextBytes(16*troubleCount.getValue());
         this.troubleCodes = IntStream.range(0, (int) Math.ceil((double) original.length / 16))
                 .mapToObj(j -> new DTC(Arrays.copyOfRange(original, j * 16, Math.min(original.length, (j + 1) * 16))))
                 .toArray(DTC[]::new);
     }

     public DTCData(U32 systemId,U16 troubleCount,byte[] troubleCodes){
         this.systemId = systemId;
         this.troubleCount = troubleCount;
         this.troubleCodes = IntStream.range(0, (int) Math.ceil((double) troubleCodes.length / 16))
                 .mapToObj(j -> new DTC(Arrays.copyOfRange(troubleCodes, j * 16, Math.min(troubleCodes.length, (j + 1) * 16))))
                 .toArray(DTC[]::new);
         setMessage(GatewayUtils.addAll(systemId.toBytes(),troubleCount.toBytes(),troubleCodes));
     }

    public U32 getSystemId() {
        return systemId;
    }

    public void setSystemId(U32 systemId) {
        this.systemId = systemId;
    }

    public U16 getTroubleCount() {
        return troubleCount;
    }

    public void setTroubleCount(U16 troubleCount) {
        this.troubleCount = troubleCount;
    }

    public DTC[] getTroubleCodes() {
        return troubleCodes;
    }

    public void setTroubleCodes(DTC[] troubleCodes) {
        this.troubleCodes = troubleCodes;
    }
}
