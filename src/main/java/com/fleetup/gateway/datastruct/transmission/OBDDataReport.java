package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: ObdData
 * @author: Justin
 * @Date: 2024-05-27
 **/
public class OBDDataReport extends BaseStructure {

    private U8 dataNumber;
    private OBDdata[] obdData;
    private U32 status;
    private U32 latitude;
    private U32 longitude;

    public OBDDataReport(byte[] message) {
        super(message);
        this.dataNumber = U8.valueOf(nextByte());
        this.obdData = new OBDdata[dataNumber.getValue()];
        for (int i = 0; i < obdData.length; i++) {
            U16 dataId = U16.valueOf(nextBytes(2));
            U8 length = U8.valueOf(nextByte());
            byte[] valueArray;
            if (length.getValue() == 0){
                valueArray = new byte[]{};
            } else {
                valueArray = nextBytes(length.getValue());
            }
            obdData[i] = new OBDdata(dataId,length,valueArray);
        }
        this.status = U32.valueOf(nextBytes(4));
        this.latitude = U32.valueOf(nextBytes(4));
        this.longitude = U32.valueOf(nextBytes(4));
    }

    public U8 getDataNumber() {
        return dataNumber;
    }

    public void setDataNumber(U8 dataNumber) {
        this.dataNumber = dataNumber;
    }

    public OBDdata[] getObdData() {
        return obdData;
    }

    public void setObdData(OBDdata[] obdData) {
        this.obdData = obdData;
    }

    public U32 getStatus() {
        return status;
    }

    public void setStatus(U32 status) {
        this.status = status;
    }

    public U32 getLatitude() {
        return latitude;
    }

    public void setLatitude(U32 latitude) {
        this.latitude = latitude;
    }

    public U32 getLongitude() {
        return longitude;
    }

    public void setLongitude(U32 longitude) {
        this.longitude = longitude;
    }
}
