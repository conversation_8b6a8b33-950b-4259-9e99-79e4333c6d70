package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;

/**
 * @className: Alarm
 * @author: Justin
 * @Date: 2024-05-28
 **/
public class Alarm extends BaseStructure {
    private U8 alarmId;
    private U8 dataLength;
    private StrF alarmValue;

    public  Alarm (byte[] message){
        super(message);
        this.alarmId = U8.valueOf(nextByte());
        this.dataLength = U8.valueOf(nextByte());
        this.alarmValue = StrF.valueOf(nextBytes(dataLength.getValue()));
    }

    public  Alarm (U8 alarmId,U8 dataLength,StrF alarmValue){
        this.alarmId = alarmId;
        this.dataLength = dataLength;
        this.alarmValue = alarmValue;
        setMessage(GatewayUtils.addAll(alarmId.toBytes(),dataLength.toBytes(),alarmValue.getMessage()));
    }

    public U8 getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(U8 alarmId) {
        this.alarmId = alarmId;
    }

    public U8 getDataLength() {
        return dataLength;
    }

    public void setDataLength(U8 dataLength) {
        this.dataLength = dataLength;
    }

    public StrF getAlarmValue() {
        return alarmValue;
    }

    public void setAlarmValue(StrF alarmValue) {
        this.alarmValue = alarmValue;
    }
}
