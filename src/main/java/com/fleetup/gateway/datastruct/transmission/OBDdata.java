package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;

/**
 * @className: OBDdata
 * @author: Justin
 * @Date: 2024-05-30
 **/
public class OBDdata extends BaseStructure {
    public final static int COMMERCIAL_ENGINE_RUNNING_HOUR = 0X0127;
    public final static int TOTAL_MILEAGE = 0X0528;
    public final static int PASSENGER_FUEL_VOLUME_L = 0X052A;
    public final static int PASSENGER_FUEL_VOLUME = 0X052B;
    public final static int PASSENGER_FUEL_CONSUMPTION_L = 0X052C;
    public final static int WATER_TEMPERATURE = 0X052D;
    public final static int PASSENGER_FUEL_CONSUMPTION_L_KM = 0X0538;
    public final static int PASSENGER_FUEL_CONSUMPTION_L_HOUR = 0X0539;
    public final static int PASSENGER_REMAINING_FUEL = 0X0544;
    public final static int VOLTAGE = 0X0530;
    public final static int SPEED = 0x0535;
    public final static int MILEAGE_ID = 0X0545;
    public final static int ACCUMULATED_MILEAGE = 0X0546;
    public final static int RPM = 0X0536;
    public final static int FUEL_CONSUMPTION = 0X0538;
    public final static int FUEL_CONSUMPTION_HOUR = 0X0539;

    private U16 dataId;
    private U8 dataLength;
    private byte[] valueArray;

    public OBDdata(byte[] message) {
        super(message);
        this.dataId = U16.valueOf(nextByte());
        this.dataLength = U8.valueOf(nextByte());
        this.valueArray = nextBytes(dataLength.getValue());
    }

    public OBDdata(U16 dataId, U8 dataLength, byte[] valueArray) {
        this.dataId = dataId;
        this.dataLength = dataLength;
        this.valueArray = valueArray;
        setMessage(GatewayUtils.addAll(dataId.toBytes(), dataLength.toBytes(), valueArray));
    }

    public U16 getDataId() {
        return dataId;
    }

    public void setDataId(U16 dataId) {
        this.dataId = dataId;
    }

    public U8 getDataLength() {
        return dataLength;
    }

    public void setDataLength(U8 dataLength) {
        this.dataLength = dataLength;
    }

    public byte[] getValueArray() {
        return valueArray;
    }

    public void setValueArray(byte[] valueArray) {
        this.valueArray = valueArray;
    }
}
