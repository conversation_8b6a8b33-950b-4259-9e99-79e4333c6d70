package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: AlarmDataReport
 * @author: Justin
 * @Date: 2024-05-28
 **/
public class AlarmDataReport extends BaseStructure {

    private U8 dataNumber;
    private Alarm[] alarms;
    private U32 status;
    private U32 latitude;
    private U32 longitude;

    public AlarmDataReport(byte[] message) {
        super(message);
        this.dataNumber = U8.valueOf(nextByte());
        this.alarms = new Alarm[dataNumber.getValue()];
        for (int i = 0; i < alarms.length; i++) {
            U8 alarmId = U8.valueOf(nextByte());
            U8 length = U8.valueOf(nextByte());
            StrF alarmValue;
            if (length.getValue() == 0){
                alarmValue = StrF.valueOf(new byte[]{});
            } else {
                alarmValue = StrF.valueOf(nextBytes(length.getValue()));
            }
            alarms[i] = new Alarm(alarmId, length, alarmValue);
        }
        this.status = U32.valueOf(nextBytes(4));
        this.latitude = U32.valueOf(nextBytes(4));
        this.longitude = U32.valueOf(nextBytes(4));

    }

    public U8 getDataNumber() {
        return dataNumber;
    }

    public void setDataNumber(U8 dataNumber) {
        this.dataNumber = dataNumber;
    }

    public Alarm[] getAlarms() {
        return alarms;
    }

    public void setAlarms(Alarm[] alarms) {
        this.alarms = alarms;
    }

    public U32 getStatus() {
        return status;
    }

    public void setStatus(U32 status) {
        this.status = status;
    }

    public U32 getLatitude() {
        return latitude;
    }

    public void setLatitude(U32 latitude) {
        this.latitude = latitude;
    }

    public U32 getLongitude() {
        return longitude;
    }

    public void setLongitude(U32 longitude) {
        this.longitude = longitude;
    }
}
