package com.fleetup.gateway.datastruct.transmission;

import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;

public class DTCDataReport extends BaseStructure {
    private U16 dataNumber;
    private DTCData[] dtcDatas;
    private U32 status;
    private U32 latitude;
    private U32 longitude;


    public DTCDataReport(byte[] message){
        super(message);
        this.dataNumber = U16.valueOf(nextBytes(2));
        this.dtcDatas = new DTCData[dataNumber.getValue()];
        for (int i = 0; i < dtcDatas.length; i++) {
            U32 systemId = U32.valueOf(nextBytes(4));
            U16 troubleCount = U16.valueOf(nextBytes(2));
            byte[] details = nextBytes(16 * troubleCount.getValue());
            dtcDatas[i] = new DTCData(systemId,troubleCount,details);
        }
        this.status = U32.valueOf(nextBytes(4));
        this.latitude = U32.valueOf(nextBytes(4));
        this.longitude = U32.valueOf(nextBytes(4));
    }

    public U16 getDataNumber() {
        return dataNumber;
    }

    public void setDataNumber(U16 dataNumber) {
        this.dataNumber = dataNumber;
    }

    public U32 getLongitude() {
        return longitude;
    }

    public void setLongitude(U32 longitude) {
        this.longitude = longitude;
    }

    public U32 getLatitude() {
        return latitude;
    }

    public void setLatitude(U32 latitude) {
        this.latitude = latitude;
    }

    public U32 getStatus() {
        return status;
    }

    public void setStatus(U32 status) {
        this.status = status;
    }

    public DTCData[] getDtcDatas() {
        return dtcDatas;
    }

    public void setDtcs(DTCData[] dtcDatas) {
        this.dtcDatas = dtcDatas;
    }
}
