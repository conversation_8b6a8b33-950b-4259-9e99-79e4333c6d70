package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.StrF;
import com.fleetup.gateway.datastruct.basic.U8;

public class VinData extends BaseStructure {

    private U8 support;
    private StrF vin;

    public VinData(byte[] message){
        super(message);
        this.support = U8.valueOf(nextByte());
        if (support.getValue() == 0x01)
            this.vin = StrF.valueOf(readRestBytes());
    }

    public U8 getSupport() {
        return support;
    }

    public void setSupport(U8 support) {
        this.support = support;
    }

    public StrF getVin() {
        return vin;
    }

    public void setVin(StrF vin) {
        this.vin = vin;
    }

}
