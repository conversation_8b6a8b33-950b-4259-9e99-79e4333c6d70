package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.constants.Constants;
import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: UplinkData
 * @author: Justin
 * @Date: 2024-05-27
 **/
public class UplinkData extends BaseStructure {

    private U8 messageType;
    private F0Data payload;


    public UplinkData(byte[] message){
        super(message);
        this.messageType = U8.valueOf(nextByte());
        if (messageType.getValue() == Constants.UPLINK_TRANSPARENT){
            this.payload = new F0Data(readRestBytes());
        }

    }

    public U8 getMessageType() {
        return messageType;
    }

    public void setMessageType(U8 messageType) {
        this.messageType = messageType;
    }

    public F0Data getPayload() {
        return payload;
    }

    public void setPayload(F0Data payload) {
        this.payload = payload;
    }

}
