package com.fleetup.gateway.datastruct.transmission;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.BCD;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: F0Data
 * @author: Justin
 * @Date: 2024-05-27
 **/
public class F0Data extends BaseStructure {

    private BCD time;
    private U8 dataType;
    private U8 vehicleType;
    private U8 messageSubcategory;
    private BaseStructure payload;

    public F0Data (byte[] message) {
        super(message);
        this.time = BCD.valueOf(nextBytes(6));
        this.dataType = U8.valueOf(nextByte());
        this.vehicleType = U8.valueOf(nextByte());
        this.messageSubcategory = U8.valueOf(nextByte());
        this.payload = new BaseStructure(readRestBytes());
    }

    public BCD getTime() {
        return time;
    }

    public void setTime(BCD time) {
        this.time = time;
    }

    public U8 getDataType() {
        return dataType;
    }

    public void setDataType(U8 dataType) {
        this.dataType = dataType;
    }

    public U8 getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(U8 vehicleType) {
        this.vehicleType = vehicleType;
    }

    public U8 getMessageSubcategory() {
        return messageSubcategory;
    }

    public void setMessageSubcategory(U8 messageSubcategory) {
        this.messageSubcategory = messageSubcategory;
    }

    public BaseStructure getPayload() {
        return payload;
    }

    public void setPayload(BaseStructure payload) {
        this.payload = payload;
    }

}
