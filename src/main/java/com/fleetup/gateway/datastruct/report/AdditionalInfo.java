package com.fleetup.gateway.datastruct.report;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;

/**
 * @className: AdditionalInfo
 * @author: Justin
 * @Date: 2024-05-19
 **/
public class AdditionalInfo extends BaseStructure {

    private U8 dataId;
    private U8 dataLength;
    private byte[] valueArray;

    public AdditionalInfo(byte[] message) {
        super(message);
        this.dataId = U8.valueOf(nextByte());
        this.dataLength = U8.valueOf(nextByte());
        this.valueArray = nextBytes(dataLength.getValue());
    }

    public AdditionalInfo(U8 dataId, U8 dataLength, byte[] valueArray) {
        this.dataId = dataId;
        this.dataLength = dataLength;
        this.valueArray = valueArray;
        setMessage(GatewayUtils.addAll(dataId.toBytes(), dataLength.toBytes(), valueArray));
    }

    public U8 getDataId() {
        return dataId;
    }

    public void setDataId(U8 dataId) {
        this.dataId = dataId;
    }

    public U8 getDataLength() {
        return dataLength;
    }

    public void setDataLength(U8 dataLength) {
        this.dataLength = dataLength;
    }

    public byte[] getValueArray() {
        return valueArray;
    }

    public void setValueArray(byte[] valueArray) {
        this.valueArray = valueArray;
    }
}
