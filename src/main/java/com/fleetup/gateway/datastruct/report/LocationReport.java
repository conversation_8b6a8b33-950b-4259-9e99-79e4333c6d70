package com.fleetup.gateway.datastruct.report;


import com.fleetup.gateway.datastruct.BaseStructure;
import com.fleetup.gateway.datastruct.basic.BCD;
import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U32;
import com.fleetup.gateway.datastruct.basic.U8;

/**
 * @className: LocationReportMO
 * @author: Justin
 * @Date: 2024-05-19
 **/
public class LocationReport extends BaseStructure {

    private U32 alarmFlag;
    private U32 status;
    private U32 latitude;
    private U32 longitude;
    private U16 elevation;
    private U16 speed;
    private U16 direction;
    private BCD time;
    private AdditionalInfo[] additionalInfoArray;

    public LocationReport(byte[] message) {
        super(message);
        this.alarmFlag = U32.valueOf(nextBytes(4));
        this.status = U32.valueOf(nextBytes(4));
        this.latitude = U32.valueOf(nextBytes(4));
        this.longitude = U32.valueOf(nextBytes(4));
        this.elevation = U16.valueOf(nextBytes(2));
        this.speed = U16.valueOf(nextBytes(2));
        this.direction = U16.valueOf(nextBytes(2));
        this.time = BCD.valueOf(nextBytes(6));
        byte[] restBytes = readRestBytes();
        backPos(restBytes.length);
        int count = countAdditionalInfo(restBytes);
        this.additionalInfoArray = new AdditionalInfo[count];
        for (int i = 0; i < additionalInfoArray.length; i++) {
            U8 dataId = U8.valueOf(nextByte());
            U8 dataLength = U8.valueOf(nextByte());
            byte[] valueArray;
            if (dataLength.getValue() == 0) {
                valueArray = new byte[]{};
            } else {
                valueArray = nextBytes(dataLength.getValue());
            }
            additionalInfoArray[i] = new AdditionalInfo(dataId, dataLength, valueArray);
        }

    }

    public static int countAdditionalInfo(byte[] data) {
        int additionalInfoCount = 0;
        int index = 0;

        while (index < data.length) {
            int additionalInfoId = data[index];
            index++;

            int additionalInfoLength = data[index];
            index++;

            index += additionalInfoLength;
            additionalInfoCount++;
        }

        return additionalInfoCount;
    }

    public U32 getAlarmFlag() {
        return alarmFlag;
    }

    public void setAlarmFlag(U32 alarmFlag) {
        this.alarmFlag = alarmFlag;
    }

    public U32 getStatus() {
        return status;
    }

    public void setStatus(U32 status) {
        this.status = status;
    }

    public U32 getLatitude() {
        return latitude;
    }

    public void setLatitude(U32 latitude) {
        this.latitude = latitude;
    }

    public U32 getLongitude() {
        return longitude;
    }

    public void setLongitude(U32 longitude) {
        this.longitude = longitude;
    }

    public U16 getElevation() {
        return elevation;
    }

    public void setElevation(U16 elevation) {
        this.elevation = elevation;
    }

    public U16 getSpeed() {
        return speed;
    }

    public void setSpeed(U16 speed) {
        this.speed = speed;
    }

    public U16 getDirection() {
        return direction;
    }

    public void setDirection(U16 direction) {
        this.direction = direction;
    }


    public AdditionalInfo[] getAdditionalInfoArray() {
        return additionalInfoArray;
    }

    public void setAdditionalInfoArray(AdditionalInfo[] additionalInfoArray) {
        this.additionalInfoArray = additionalInfoArray;
    }

    public BCD getTime() {
        return time;
    }

    public void setTime(BCD time) {
        this.time = time;
    }
}
