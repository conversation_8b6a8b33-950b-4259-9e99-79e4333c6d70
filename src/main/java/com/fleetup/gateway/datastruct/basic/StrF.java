package com.fleetup.gateway.datastruct.basic;


/**
 * @className: StrF
 * @author: Justin
 * @Date: 2024-05-17
 **/
public class StrF {
    private byte[] message;

    public StrF(byte[] message){
        this.message = message;
    }
    public StrF(byte b1, byte b2, byte b3, byte b4){
        this.message = new byte[]{b1, b2, b3, b4};
    }

    public static StrF valueOf(byte[] message){
        return new StrF(message);
    }

    public static StrF valueOf(byte b1, byte b2, byte b3, byte b4){
        return new StrF(b1,b2,b3,b4);
    }

    public static StrF valueOf(String str, int length){
        byte[] bytes = new byte[length];
        byte[] strBytes = str.getBytes();
        System.arraycopy(strBytes, 0, bytes, 0, strBytes.length);
        return new StrF(bytes);
    }

    public static StrF valueOf20(String str){
        return valueOf(str, 20);
    }

    public String getValue(){
        int length = 0;
        for (byte b : message) {
            if (b == 0x00) {
                break;
            } else {
                length++;
            }
        }
        return new String(message, 0, length);
    }

    public byte[] getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return String.valueOf(getValue());
    }
}
