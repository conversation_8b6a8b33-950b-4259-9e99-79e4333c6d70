package com.fleetup.gateway.datastruct.basic;


/**
 * @className: U8
 * @author: <PERSON>
 * @Date: 2024-05-16
 **/
public class U8 {
    private byte message;
    public U8(byte message){
        this.message = message;
    }

    public U8(Integer value){
        this.message = value.byteValue();
    }

    public static U8 valueOf(Integer value){
        return new U8(value.byteValue());
    }
    public static U8 valueOf(byte message){
        return new U8(message);
    }

    public int getValue(){
        return message & 0xFF;
    }

    public byte toByte(){
        return message;
    }

    public byte[] toBytes(){
        return new byte[]{message};
    }

    @Override
    public String toString() {
        return String.valueOf(getValue());
    }
}
