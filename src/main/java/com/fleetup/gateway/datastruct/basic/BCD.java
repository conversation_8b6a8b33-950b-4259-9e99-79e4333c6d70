package com.fleetup.gateway.datastruct.basic;


import com.fleetup.gateway.util.Hex;

import java.math.BigInteger;

/**
 * @className: BCD
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class BCD {
    private byte[] message;

    public BCD(byte[] message){
        this.message = message;
    }

    public static BCD valueOf(byte[] message){
        return new BCD(message);
    }

    public static BCD valueOf(String value){
        return new BCD(Hex.fromHexString(value));
    }

    public String getValue(){
        BigInteger decimalValue = new BigInteger(Hex.toHexString(message), 16);
        return decimalValue.toString();
    }

    public String readValue(){
        return Hex.toHexString(message);
    }

    public byte[] toBytes() {
        return message;
    }


    @Override
    public String toString() {
        return getValue();
    }
}