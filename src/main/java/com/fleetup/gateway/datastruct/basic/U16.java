package com.fleetup.gateway.datastruct.basic;


import com.fleetup.gateway.util.Hex;

/**
 * @className: U16
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class U16 {
    private byte[] message;
    private boolean isBigEndian = true;

    public U16(byte[] message){
        this.message = message;
    }
    public U16(byte b1, byte b2){
        this.message = new byte[]{b1, b2};
    }

    public U16(int value){
        this(value, true);
    }

    public U16(int value, boolean bigEndian){
        this.isBigEndian = bigEndian;
        if (bigEndian) {
            this.message = new byte[]{(byte) (value >>> 8), (byte)value};
        } else {
            this.message = new byte[]{(byte)value, (byte) (value >>> 8)};
        }
    }

    public static U16 valueOf(int value){
        return new U16(value);
    }
    public static U16 valueOf(byte[] message){
        return new U16(message);
    }
    public static U16 valueOf(byte b1, byte b2){
        return new U16(b1,b2);
    }

    public int getValue(){
        return getValue(this.isBigEndian);
    }

    public int getValue(boolean bigEndian){
        if (bigEndian) {
            return ((message[0] & 0xFF) << 8) + ((message[1] & 0xFF));
        } else {
            return ((message[1] & 0xFF) << 8) + (message[0] & 0xFF);
        }
    }

    public byte[] toBytes() {
        return message;
    }

    @Override
    public String toString() {
        return toString(this.isBigEndian);
    }

    public String toString(boolean bigEndian) {
        return Integer.toString(getValue(bigEndian));
    }

    public String toHexString() {
        return toHexString(this.isBigEndian);
    }

    public String toHexString(boolean bigEndian) {
        return "0x"+ Hex.toHexString(message);
    }
}
