package com.fleetup.gateway.datastruct.basic;


import com.fleetup.gateway.util.Hex;

/**
 * @className: U32
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class U32 {
    private byte[] message;


    public U32(byte[] message) {
        this.message = message;
    }

    public U32(byte b1, byte b2, byte b3, byte b4) {
        this.message = new byte[]{b1, b2, b3, b4};
    }

    public U32(int value) {
        this.message = new byte[]{(byte) (value >> 24), (byte) (value >> 16), (byte) (value >> 8), (byte) value};
    }

    public U32(long value) {
        this.message = new byte[]{(byte) (value >> 24), (byte) (value >> 16), (byte) (value >> 8), (byte) value};
    }

    public static U32 valueOf(byte[] message) {
        return new U32(message);
    }

    public static U32 valueOf(byte b1, byte b2, byte b3, byte b4) {
        return new U32(b1, b2, b3, b4);
    }

    public long getValue() {
        long result = 0L;
        result += ((message[0] & 0xFFL) << 24);
        result += ((message[1] & 0xFFL) << 16);
        result += ((message[2] & 0xFFL) << 8);
        result += (message[3] & 0xFFL);

        return result;
    }

    public byte[] toBytes() {
        return message;
    }

    @Override
    public String toString() {
        return String.valueOf(getValue());
    }
}
