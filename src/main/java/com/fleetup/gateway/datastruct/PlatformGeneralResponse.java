package com.fleetup.gateway.datastruct;


import com.fleetup.gateway.datastruct.basic.U16;
import com.fleetup.gateway.datastruct.basic.U8;
import com.fleetup.gateway.util.GatewayUtils;
import com.google.common.base.MoreObjects;

/**
 * 0x8001
 * @className: GeneralResponse
 * @author: Justin
 * @Date: 2024-05-16
 **/
public class PlatformGeneralResponse extends BaseStructure {
    private U16 respSeqNumber;
    private U16 respMessageId;
    private U8  result;

    public PlatformGeneralResponse(byte[] message){
        super(message);
        this.respSeqNumber = U16.valueOf(nextBytes(2));
        this.respMessageId = U16.valueOf(nextBytes(2));
        this.result = U8.valueOf(nextByte());
    }

    public PlatformGeneralResponse(int respSeqNumber,int protocolId,int result){
        this.setRespSeqNumber(new U16(respSeqNumber));
        this.setRespMessageId(new U16(protocolId));
        this.setResult(new U8(result));
        this.setMessage(GatewayUtils.addAll(this.respSeqNumber.toBytes(),this.respMessageId.toBytes(),this.result.toBytes()));
    }

    public U16 getRespSeqNumber() {
        return respSeqNumber;
    }

    public void setRespSeqNumber(U16 respSeqNumber) {
        this.respSeqNumber = respSeqNumber;
    }

    public U16 getRespMessageId() {
        return respMessageId;
    }

    public void setRespMessageId(U16 respMessageId) {
        this.respMessageId = respMessageId;
    }

    public U8 getResult() {
        return result;
    }

    public void setResult(U8 result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("respSeqNumber", this.respSeqNumber)
                .add("respMessageId", this.respMessageId)
                .add("result", this.result)
                .toString();
    }

}
