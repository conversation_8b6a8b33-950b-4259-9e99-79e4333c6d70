package com.fi.lambda.service.dtc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;

import lombok.extern.log4j.Log4j2;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Log4j2
public class DtcCodeCache {

    public static final DtcCodeCache cache = new DtcCodeCache();

    private static final String DEFAULT_DTC_CODE_LOCATION = "stdcode/ADM_DTC_MESSAGE.json";

    private final  Map<Integer, List<Integer>> cacheMap = Maps.newConcurrentMap();

    private DtcCodeCache() {
        init();
    }

    public boolean exist(int carType, int dtcCode) {
        return cacheMap.get(carType).contains(dtcCode);
    }

    void init() {
        try {
            InputStream resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(DEFAULT_DTC_CODE_LOCATION);
            ObjectMapper mapper = new ObjectMapper();
            List<DtcCode> dtcList = mapper.readValue(resourceAsStream, new TypeReference<List<DtcCode>>(){});

            for (DtcCode dtcCode : dtcList) {
                List<Integer> dtcCodes = cacheMap.get(dtcCode.getCarType());
                if (dtcCodes == null) {
                    dtcCodes = new ArrayList<>();
                    dtcCodes.add(dtcCode.getCode());
                    cacheMap.put(dtcCode.getCarType(), dtcCodes);
                } else {
                    if (!dtcCodes.contains(dtcCode.getCode())) {
                        dtcCodes.add(dtcCode.getCode());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Read dtc data fail, location:" + DEFAULT_DTC_CODE_LOCATION + ", error:" + e.getMessage());
            throw new RuntimeException("Lambda init fail! Please check dtc code config.");
        }
    }


    public static void main(String[] args) {
        boolean exist = DtcCodeCache.cache.exist(2, 565);
    }
}
