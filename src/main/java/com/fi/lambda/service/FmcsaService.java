package com.fi.lambda.service;

import com.fi.gateway.producer.event.Gps;
import com.fi.lambda.common.aws.SQSClient;
import com.fi.lambda.common.aws.SQSService;
import com.fi.lambda.model.DashboardCache;
import com.fi.lambda.model.FmcsaEvent;
import com.fi.lambda.model.HosHistoryCache;
import com.fi.lambda.utils.DateTimeUtils;
import com.fleetup.rds.Env;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class FmcsaService {
	private static final String ELD_EVENT_POSTFIX = "-eld-events.fifo";
	private static final String HOS_EVENT_POSTFIX = "-hos-events.fifo";
	private static final int SPEED_THRESHOLD = 220; // Management team decided to use 220 cm/s for 5 mph threshold.
	
	// Driving Status
	private static final short NIM = 0; // Not-in-motion status
	private static final short FIVE_MIN = 1; // Not-in-motion and 5-minute idling event was sent
	private static final short ONE_MIN = 2; // No response more than 1 minute after 5-minute idling event was sent
	private static final short IM = 5; // Regular in-motion status
	private static final short IM_FLAG_FIVE_MIN = 6; // In-motion status with flag to indicates the previous status was FIVE_MIN
	private static final short IM_FLAG_ONE_MIN = 7; // In-motion status with flag to indicates the previous status was ONE_MIN
	
	// ELD Event Type
	private static final int ENGINE_ON = 1;
	private static final int DRIVING = 2;
	private static final int INTERMEDIATE_DRIVING = 3;
	private static final int NOT_IN_MOTION = 4;
	private static final int IDLING = 5;
	private static final int ENGINE_OFF = 6;
	private static final int SIGNAL_BACK = 7;
	
	// HOS Event Type
	private static final int IDLING_FORCE_START = 11;
	
	private static final short DATAPOINT_THRESHOLD = 2; // Change status when receive this number of datapoints.
	
	private SQSService eldSqsService;
	private SQSService hosSqsService;

	public void init() {
		setSQSService(new SQSService(SQSClient.getInstance().getSqs()));
		setHosSQSService(new SQSService(SQSClient.getInstance().getSqs()));
		hosSqsService.setQueueUrl(Env.getAccountId(), Env.getRegion(), Env.getStage(), HOS_EVENT_POSTFIX);
	}
	
	public void setHosSQSService(SQSService sqsService) {
		this.hosSqsService = sqsService;
	}
	
	public void setSQSService(SQSService sqsService) {
		this.eldSqsService = sqsService;
		eldSqsService.setQueueUrl(Env.getAccountId(), Env.getRegion(), Env.getStage(), ELD_EVENT_POSTFIX);
	}
	
	public SQSService getSQSService() {
		return this.eldSqsService;
	}
	
	public void startNewTrip(String devId, DashboardCache curCache, boolean isBatch, Gps startGps) {
		if (curCache.getEldTmTime() == null || curCache.getTmTime() > curCache.getEldTmTime()) {
			// Reset ELD attributes
			curCache.setEldStatus(NIM);
			curCache.setEldCount((short) 0);
			curCache.setEldTmTime(curCache.getTmTime());
			curCache.setEldIdleTmTime(curCache.getTmTime());
			curCache.setHasDriven((short) 0);
		}

		FmcsaEvent fmcsaEvent;
		if (!isBatch) {
			// Add Engine-on event to SQS batch
			fmcsaEvent = FmcsaEvent.builder()
					.devId(devId).acconTime(curCache.getAcconTime())
					.tmTime(curCache.getTmTime()).speed(curCache.getSpeed())
					.lat(curCache.getLat()).lng(curCache.getLng())
					.isHistory((short) 0)
					.eventType(ENGINE_ON)
					.eventTime(DateTimeUtils.getUTCNow(DateTimeUtils.DTF_yyyyMMddHHmmss))
					.build();

		} else {
			// Fix FF-2914: Handle trips which the 1st event is sent in a batch
			fmcsaEvent = FmcsaEvent.builder()
					.devId(devId).acconTime(curCache.getAcconTime())
					.tmTime(startGps.getTmTime()).speed(startGps.getSpeed())
					.lat(startGps.getLatitude()).lng(startGps.getLongitude())
					.isHistory((short) 1)
					.eventType(ENGINE_ON)
					.eventTime(DateTimeUtils.getUTCNow(DateTimeUtils.DTF_yyyyMMddHHmmss))
					.build();
		}
		eldSqsService.addToMessageList(fmcsaEvent);
		fmcsaEvent.setTripMileage(curCache.getTripMileage());
		fmcsaEvent.setDeviceMileage(curCache.getDeviceMileage());
		fmcsaEvent.setTotalFuelConsu(curCache.getTotalFuelConsu());
		hosSqsService.addToMessageList(fmcsaEvent);
	}

	/**
	 * Generate ELD events for ongoing trips.
	 * @param devId
	 * @param curCache
	 * @param prevCache
	 */
	public void processOngoingTrips(String devId, DashboardCache curCache, DashboardCache prevCache, boolean isBatch) {
		if (curCache.getTmTime() > curCache.getEldTmTime()) {
			if (isBatch) {
				if (prevCache.getStatus() != null && prevCache.getStatus() == 2) {
					// FF-10265 Send SIGNAL_BACK event for aggregated events.
					FmcsaEvent fmcsaEvent = generateEldEvent(devId, curCache, SIGNAL_BACK);
					fmcsaEvent.setTripMileage(curCache.getTripMileage());
					fmcsaEvent.setDeviceMileage(curCache.getDeviceMileage());
					fmcsaEvent.setFuelWear(curCache.getFuelWear());
					fmcsaEvent.setTotalFuelConsu(curCache.getTotalFuelConsu());
					hosSqsService.addToMessageList(fmcsaEvent);
				}
			} else {
				// [FF-4743] Send signal-back events when trip was ended by scheduler-endTrip.
				if (prevCache.getStatus() != null && prevCache.getStatus() == 2) {
					FmcsaEvent fmcsaEvent = generateEldEvent(devId, curCache, SIGNAL_BACK);
					eldSqsService.addToMessageList(fmcsaEvent);

					fmcsaEvent.setTripMileage(curCache.getTripMileage());
					fmcsaEvent.setDeviceMileage(curCache.getDeviceMileage());
					fmcsaEvent.setFuelWear(curCache.getFuelWear());
					fmcsaEvent.setTotalFuelConsu(curCache.getTotalFuelConsu());
					hosSqsService.addToMessageList(fmcsaEvent);
				}

				if (curCache.getSpeed() >= SPEED_THRESHOLD) {
					// >= 5 mph
					processInMotionEvent(devId, curCache, prevCache);
				} else {
					// < 5 mph
					processNotInMotionEvent(devId, curCache, prevCache);
				}
			}

		}
	}

	/**
	 * Send Batch Messages to SQS
	 */
	public void sendBatchMessageToSQS() {
		eldSqsService.sendBatchMessageToSQS();
		hosSqsService.sendBatchMessageToSQS();
	}
	
	/**
	 * Process in-motion event
	 * @param devId
	 * @param curCache
	 */
	private void processInMotionEvent(String devId, DashboardCache curCache, DashboardCache prevCache) {
		if (curCache.getEldStatus() >= IM) {
			curCache.setEldCount((short) (curCache.getEldCount() + 1));
			
			if (curCache.getEldCount() == DATAPOINT_THRESHOLD) {
				// Add Driving event to SQS Batch
				FmcsaEvent fmcsaEvent = generateEldEvent(devId, curCache, DRIVING);
				eldSqsService.addToMessageList(fmcsaEvent);

				fmcsaEvent = generateEldEvent(devId, prevCache, DRIVING);
				fmcsaEvent.setTripMileage(prevCache.getTripMileage());
				fmcsaEvent.setDeviceMileage(prevCache.getDeviceMileage());
				fmcsaEvent.setFuelWear(prevCache.getFuelWear());
				fmcsaEvent.setTotalFuelConsu(prevCache.getTotalFuelConsu());
				hosSqsService.addToMessageList(fmcsaEvent);
				
				curCache.setHasDriven((short) 1);
				curCache.setEldTmTime(curCache.getTmTime());
				curCache.setEldStatus(IM); // Reset the status
			} else if (curCache.getEldCount() > DATAPOINT_THRESHOLD
					&& DateTimeUtils.minDifference(curCache.getEldTmTime(), curCache.getTmTime(), DateTimeUtils.DTF_yyyyMMddHHmmss) >= 60) {
				// Add Intermediate Driving event to SQS Batch
				eldSqsService.addToMessageList(generateEldEvent(devId, curCache, INTERMEDIATE_DRIVING));
				
				curCache.setEldCount((short) DATAPOINT_THRESHOLD); // To avoid sending unnecessary Driving events
				curCache.setEldTmTime(curCache.getTmTime());
			}
		} else {
			if (curCache.getHasDriven() == 1
					&& curCache.getEldStatus() == NIM
					&& curCache.getEldCount() < DATAPOINT_THRESHOLD) {
				curCache.setEldCount(DATAPOINT_THRESHOLD);
			} else {
				curCache.setEldCount((short) 1);
			}
			
			// Change back to the correct in-motion status if the device is in not-in-motion status.
			curCache.setEldStatus((short) (curCache.getEldStatus() + IM));
		}
	}
	
	/**
	 * Process not-in-motion event
	 * @param devId
	 * @param curCache
	 */
	private void processNotInMotionEvent(String devId, DashboardCache curCache, DashboardCache prevCache) {
		if (curCache.getEldStatus() == NIM) {
			curCache.setEldCount((short) (curCache.getEldCount() + 1));
			
			// Do not send no-in-motion event when the vehicle has never been driven in the current trip
			if (curCache.getHasDriven() == 1) {
				if (curCache.getEldCount() == DATAPOINT_THRESHOLD) {
					// Add Not-in-motion event to SQS batch
					FmcsaEvent fmcsaEvent = generateEldEvent(devId, curCache, NOT_IN_MOTION);
					eldSqsService.addToMessageList(fmcsaEvent);

					fmcsaEvent.setTripMileage(curCache.getTripMileage());
					fmcsaEvent.setDeviceMileage(curCache.getDeviceMileage());
					fmcsaEvent.setFuelWear(curCache.getFuelWear());
					fmcsaEvent.setTotalFuelConsu(curCache.getTotalFuelConsu());
					fmcsaEvent.setTmTime(prevCache.getTmTime());
					hosSqsService.addToMessageList(fmcsaEvent);
					
					curCache.setEldIdleTmTime(curCache.getTmTime());
				} else if (curCache.getEldCount() > DATAPOINT_THRESHOLD
						&& DateTimeUtils.minDifference(curCache.getEldIdleTmTime(), curCache.getTmTime(), DateTimeUtils.DTF_yyyyMMddHHmmss) == 5) {
					/**
					 * [2020/2/20] Send 5-minutes idling event only when the vehicle idles for exactly 5 minutes.
					 */
					// Add 5-minutes Idling event to SQS batch
					FmcsaEvent fmcsaEvent = generateEldEvent(devId, curCache, IDLING);
					eldSqsService.addToMessageList(fmcsaEvent);
					
					fmcsaEvent.setTripMileage(curCache.getTripMileage());
					fmcsaEvent.setDeviceMileage(curCache.getDeviceMileage());
					fmcsaEvent.setFuelWear(curCache.getFuelWear());
					fmcsaEvent.setTotalFuelConsu(curCache.getTotalFuelConsu());
					hosSqsService.addToMessageList(fmcsaEvent);
					
					curCache.setEldIdleTmTime(curCache.getTmTime());
					curCache.setEldStatus(FIVE_MIN);
				}
			}
		} else if (curCache.getEldStatus() == FIVE_MIN) {
			curCache.setEldCount((short) (curCache.getEldCount() + 1));
			if (DateTimeUtils.minDifference(curCache.getEldIdleTmTime(), curCache.getTmTime(), DateTimeUtils.DTF_yyyyMMddHHmmss) >= 1) {
				FmcsaEvent fmcsaEvent = FmcsaEvent.builder()
						.devId(devId)
						.acconTime(curCache.getAcconTime())
						.tmTime(curCache.getTmTime())
						.speed(curCache.getSpeed())
						.lat(curCache.getLat())
						.lng(curCache.getLng())
						.tripMileage(curCache.getTripMileage())
						.deviceMileage(curCache.getDeviceMileage())
						.fuelWear(curCache.getFuelWear())
						.totalFuelConsu(curCache.getTotalFuelConsu())
						.eventType(IDLING_FORCE_START)
						.eventTime(DateTimeUtils.getUTCNow(DateTimeUtils.DTF_yyyyMMddHHmmss))
						.build();
				hosSqsService.addToMessageList(fmcsaEvent);
				curCache.setEldIdleTmTime(curCache.getTmTime());
				curCache.setEldStatus(ONE_MIN);
			}
		} else if (curCache.getEldStatus() == ONE_MIN) {
			curCache.setEldCount((short) (curCache.getEldCount() + 1));
		} else {
			if (curCache.getHasDriven() == 1
					&& curCache.getEldCount() < DATAPOINT_THRESHOLD) {
				// Change back to the correct not-in-motion status if the count of in-motion events did not reach threshold. 
				curCache.setEldStatus((short) (curCache.getEldStatus() % IM));
				curCache.setEldCount(DATAPOINT_THRESHOLD);
			} else {
				curCache.setEldStatus(NIM);
				curCache.setEldCount((short) 1);
			}
		}
	}
	
	/**
	 * Generate {@link FmcsaEvent} object.
	 * @param devId Device ID
	 * @param cache {@link DashboardCache} object which will be stored in Redis
	 * @param eventType 1: Engine On, 2: Driving, 3: Intermediate Driving (Every 1 hour of driving), 4: speed < 220cm/s, 5: 5-minutes Idling, 6: Engine OFF by endTrip
	 * @return
	 */
	private FmcsaEvent generateEldEvent(String devId, DashboardCache cache, Integer eventType) {
		return FmcsaEvent.builder()
				.devId(devId)
				.acconTime(cache.getAcconTime())
				.tmTime(cache.getTmTime())
				.speed(cache.getSpeed())
				.lat(cache.getLat())
				.lng(cache.getLng())
				.eventType(eventType)
				.eventTime(DateTimeUtils.getUTCNow(DateTimeUtils.DTF_yyyyMMddHHmmss))
				.build();
	}
}
