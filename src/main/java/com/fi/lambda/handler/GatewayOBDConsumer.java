package com.fi.lambda.handler;


import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.amazonaws.util.NumberUtils;
import com.amazonaws.util.json.Jackson;
import com.fi.gateway.producer.event.*;
import com.fi.lambda.common.Constants;
import com.fi.lambda.common.aws.DynamoDbClient;
import com.fi.lambda.common.aws.SQSClient;
import com.fi.lambda.dao.*;
import com.fi.lambda.model.*;
import com.fi.lambda.service.dtc.DtcCodeCache;
import com.fi.lambda.utils.ConvertUtil;
import com.fi.lambda.utils.DateTimeUtils;
import com.fi.lambda.utils.JsonUtils;
import com.fleetup.common.DTCService;
import com.fleetup.common.FileDTCService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang.StringUtils;

import com.amazonaws.kinesis.deagg.RecordDeaggregator;
import com.amazonaws.services.kinesis.clientlibrary.types.UserRecord;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.KinesisEvent;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fi.gateway.producer.service.EnvConfig;
import com.fi.gateway.producer.service.KinesisProducerService;
import com.fi.lambda.common.Constants.AlarmType;
import com.fi.lambda.common.Constants.HTRet;
import com.fi.lambda.common.Constants.PID;
import com.fi.lambda.common.Constants.Protocol;
import com.fi.lambda.common.TripEnum;
import com.fi.lambda.common.aws.SQSService;
import com.fi.lambda.common.redis.service.LettuceService;
import com.fi.lambda.service.FmcsaService;
import com.fleetup.rds.Env;

@Log4j2
public class GatewayOBDConsumer implements RequestHandler<KinesisEvent, Void> {
    private static final int GATEWAY_ID = 1;
    private static final int FUEL_MAX = 65_535;
    private static final int MAX_KPL = 60; // 141 MPG (Tesla Model 3)
    private static final double MAX_IDLING_FW = 6.4; // 0.01L per second. Minimum Fuel Wear Increment is 0.5L and the GPS interval can be 9-11 seconds.
    private Set<Integer> alarmTypesToFilter = new HashSet<>(Arrays.asList(27, 28, 17));

    private static final String TTL_DEV_ACCON = "TRIP_%s_%s";
    private static final String HOS_HISTORY_DEV_ACCON = "HOSHISTORY:%s:%s";
    private static final String LIVE_ETA_DEV = "LIVEETA:%s";
    private static final String SQS_POSTFIX = "-trip-conversion"; // (ex) stress-trip-conversion, prod-trip-conversion
//    private static final String SQS_ALERT_NOTIFICATION_POSTFIX = "-alert-notification"; // (ex) stress-alert-notification, prod-alert-notification

    private static final String REDIS_HASH_KEY_LAST_EVENT_TIME = "lastEventTime";

    // Disable GPS_DATA insertion if set to true, else enable.
    private static final boolean GPS_DISABLED = Boolean.parseBoolean(System.getenv("GPS_DISABLED"));
    private static final boolean GEOFENCE_DISABLED = Boolean.parseBoolean(System.getenv("GEOFENCE_DISABLED"));

    private static final int END_TRIP_THRESHOLD = Optional.ofNullable(System.getenv("END_TRIP_THRESHOLD")).filter(com.amazonaws.util.StringUtils::hasValue).map(NumberUtils::tryParseInt).orElse(5);


    private static final String DEFAULT_CHANNEL_NAME = "GW:3vl:REMOTE";

    private static final String CHANNEL_NAME = System.getenv().containsKey("CHANNEL_NAME") ? System.getenv("CHANNEL_NAME") : DEFAULT_CHANNEL_NAME;

    private FotaDao fotaDao = new FotaDao();
    private TripDao tripDao = new TripDao();
    private DeviceDao deviceDao = new DeviceDao();
    private AlarmDao alarmDao = new AlarmDao();
    private final TLVDao tlvDao = new TLVDao();
    private AlarmDataDynamoDbDao alarmDynamoDbDao = new AlarmDataDynamoDbDao(DynamoDbClient.getInstance().getMapper());
    private BatteryDao batteryDao = new BatteryDao();
    private GpsDataDynamoDdbDao gpsDataDynamoDdbDao = new GpsDataDynamoDdbDao(DynamoDbClient.getInstance().getMapper());
    private PidDataDao pidDataDao = new PidDataDao();
    private DTCService dtcService = new FileDTCService();
    private LettuceService lettuceService;
    private KinesisProducerService kinesisThirdPartyProducer;
    private Map<String, Long> partnerDeviceIdData;
    private List<LogoutEvent> logoutDataList = new ArrayList<>();
    private SQSService sqsService = new SQSService(SQSClient.getInstance().getSqs());
    //    private SQSService sqsAlertNotification = new SQSService(SQSClient.getInstance().getSqs());
    private FmcsaService fmcsaService = new FmcsaService();

    private void lazyInit(Context context) {
        Env.init(context.getInvokedFunctionArn());
        EnvConfig.init();
        gpsDataDynamoDdbDao.setTableNamePrefix(Env.getStage());
        alarmDynamoDbDao.setTableNamePrefix(Env.getStage());
        if (sqsService.getQueueUrl() == null) {
            sqsService.setQueueUrl(Env.getAccountId(), Env.getRegion(), Env.getStage(), SQS_POSTFIX);
        }
//        if (!Env.getStage().equalsIgnoreCase("prod") && sqsAlertNotification.getQueueUrl() == null) {
//        	sqsAlertNotification.setQueueUrl(Env.getAccountId(), Env.getRegion(), Env.getStage(), SQS_ALERT_NOTIFICATION_POSTFIX);
//		}
        if (fmcsaService.getSQSService() == null)
            fmcsaService.init();

        if (lettuceService == null) {
            lettuceService = new LettuceService();
            try {
                lettuceService.initConnectionFactory();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        if (EnvConfig.isInitialized()) {
            if (kinesisThirdPartyProducer == null) {
                kinesisThirdPartyProducer = new KinesisProducerService(EnvConfig.getKinesisEndpoint());
            }
            if (partnerDeviceIdData == null || partnerDeviceIdData.isEmpty()) {
                partnerDeviceIdData = deviceDao.getDeviceIdByAcctId(EnvConfig.getAccountIds());
                if (partnerDeviceIdData != null)
                    log.info("Loading partner Data : size : " + partnerDeviceIdData.size());
                else
                    log.debug("Not found partner device data");
            }
        }
    }

    @Override
    public Void handleRequest(KinesisEvent event, Context context) {
        lazyInit(context);

        log.info("Received " + event.getRecords().size() + " raw Event Records.");

        List<UserRecord> userRecords = RecordDeaggregator.deaggregate(event.getRecords());

        // used for batch update device connection stat
        Map<String, String> connectionStatMap = new HashMap<>();

        for (UserRecord userRecord : userRecords) {
            try {
                ByteBuffer byteBuffer = userRecord.getData();
                String jsonStr = new String(byteBuffer.array());
                log.debug("Parsed JSON: " + jsonStr);
                EventMessage<?> eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, EventMessage.class);

                if (eventMessage.getCurrentTime() != null && eventMessage.getDevId() != null) {
                    String existTime = connectionStatMap.get(eventMessage.getDevId());
                    if (existTime == null) {
                        connectionStatMap.put(eventMessage.getDevId(), eventMessage.getCurrentTime());
                    } else {
                        long l = DateTimeUtils.differenceInSeconds(existTime, eventMessage.getCurrentTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        if (l > 0) {
                            connectionStatMap.put(eventMessage.getDevId(), eventMessage.getCurrentTime());
                        }
                    }
                }

                handleObdEvent(jsonStr, eventMessage);
                if (EnvConfig.isInitialized() && partnerDeviceIdData != null
                        && partnerDeviceIdData.containsKey(eventMessage.getDevId())) {
                    kinesisThirdPartyProducer.addRecords(byteBuffer);
                }
            } catch (Exception e) {
                log.error("[ERROR RECORD] " + event.getRecords().size() + "] "
                        + "(" + userRecord.getSequenceNumber()
                        + ", " + userRecord.getSubSequenceNumber() + ")"
                        + new String(userRecord.getData().array()), e);
            }
        }

        // batch update connection stat
        batchUpdateConnectionStat(connectionStatMap);

        batchProcess();
        if (EnvConfig.isInitialized() || !GEOFENCE_DISABLED) {
            publishToKinesis(kinesisThirdPartyProducer);
        }

        return null;
    }

    private void batchUpdateConnectionStat(Map<String, String> connectionStatMap) {
        long startTime = System.currentTimeMillis();
        lettuceService.batchHsetAsync(connectionStatMap, REDIS_HASH_KEY_LAST_EVENT_TIME);
        log.info("batchUpdateConnectionStat() cost " + (System.currentTimeMillis() - startTime) + "ms");
    }

    private void publishToKinesis(KinesisProducerService kinesisProducer) {
        if (kinesisProducer != null) {
            kinesisProducer.publishRecord();
        }
    }

    /**
     * Handle a Kinesis request and process it using a bulk processing method.
     *
     * @param eventMessage
     * @param jsonStr
     */
    void handleObdEvent(String jsonStr, EventMessage<?> eventMessage) {
        try {
            if (eventMessage == null || eventMessage.getDevId() == null) {
                log.warn("eventMessage is null or doesn't contain devId.");
                return;
            }

            switch (eventMessage.getProtocolId()) {
                case Protocol.LOGIN:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<LoginEvent>>() {
                    });
                    loginProcess(eventMessage);
                    break;
                case Protocol.LOGOUT:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<LogoutEvent>>() {
                    });
                    logoutProcess(eventMessage);
                    break;
                case Protocol.GPS_DATA:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<GpsEvent>>() {
                    });
                    GpsEvent gpsEvent = (GpsEvent) eventMessage.getData();
                    if (gpsEvent.getAccStatus() == 0) {
                        log.info("Filtered out the data due to ACC OFF.");
                        return;
                    }
                    String devId = eventMessage.getDevId();

                    // Check if trip exist in Redis
                    Map<String, String> map = lettuceService.hgetallSync(devId);
                    DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);

                    int[] gpsBound = gpsProcess(devId, gpsEvent, cache);

                    // If gps[] is not empty
                    if (gpsBound[0] >= 0 && gpsBound[1] >= 0) {
                        statDataProcess(devId, gpsEvent, cache, gpsBound);
                    } else {
                        log.warn("GPS data is invalid.");
                    }

                    break;
//                case Protocol.TRAVEL_DATA_REPORT:
//                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<TravelDataEvent>>() {
//                    });
//                    travelProcess(eventMessage);
//                    break;
                case Protocol.PID_DATA: // HGCT_OBD_UP_CON
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<PidEvent>>() {
                    });
                    conProcess(eventMessage);
                    break;
                case Protocol.DBS_DTC:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<DtcEvent>>() {
                    });
                    faultPassengerProcess(eventMessage);
                    break;
//                case Protocol.DTCS_OF_PASSENGER_CAR: // HGCT_OBD_UP_FAULT
//                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<DtcEvent>>() {
//                    });
//                    faultPassengerProcess(eventMessage);
//                    break;
//                case Protocol.DTCS_OF_COMMERCIAL_VEHICLE: // HGCT_OBD__FAULT_COMMERCIAL
//                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<DtcEvent>>() {
//                    });
//                    faultCommercialProcess(eventMessage);
//                    break;
                case Protocol.ALARM:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<AlarmEvent>>() {
                    });
                    alarmProcess(eventMessage);
                    break;
                case Protocol.SUPPORTED_PID_TYPES:
                    // TODO what to do with this?
                    break;
                case Protocol.UPGRADE_NOTIFY:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<UpgradeNotifyEvent>>() {
                    });
                    upgradeNotifyProcess(eventMessage);
                    break;
                case Protocol.UPGRADE_PROCESS:
                    eventMessage = JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<UpgradeProgressEvent>>() {
                    });
                    upgradeProgressProcess(eventMessage);
                    break;
                case Protocol.DBS_ALARM_CONFIG_STATUS:
                    alarmConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_FUEL_PARAM_CONFIG_STATUS:
                    fuelParamConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_OMMODE_CONFIG_STATUS:
                    workModeConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_PODELAY_CONFIG_STATUS:
                    powerOffdelayConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_SYNCH_UTC:
                    synchUtcProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_ENGINE_PARAM_CONFIG_STATUS:
                    engineParamConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.DBS_UPLOAD_CONFIG_STATUS:
                    uploadConfigProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, Integer>>>() {
                    }));
                    break;
                case Protocol.CUSTOM_EVENT_QUERY_WORK_MODE:
                    queryWorkModeProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<QueryWorkModeEvent>>() {
                    }));
                    break;
                case Protocol.CUSTOM_EVENT_FOTA:
                    customFotaProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<CustomFotaEvent>>() {
                    }));
                    break;
                case Protocol.UPGRADE_MO_HTTP_FTP_NOTIFICATION:
                    httpFtpUpgradeNotify(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<FotaHttpFtpNotificationEvent>>() {
                    }));
                    break;
                case Protocol.UPGRADE_MO_HTTP_FTP_DOWNLOAD_PROGRESS:
                    httpFtpUpgradeProgress(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<FotaHttpFtpDownloadProgressEvent>>() {
                    }));
                    break;
                case Protocol.UPGRADE_MO_HTTP_FTP_RESULT:
                    httpFtpUpgradeResult(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<FotaHttpFtpResultEvent>>() {
                    }));
                    break;
                case Protocol.CUSTOM_EVENT_TLV_COMMON_QUERY:
                    commonTlvEventProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<CommonTlvEvent>>() {
                    }));
                    break;
                case Protocol.DBS_VINCODE:
                    vinCodeEventProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, String>>>() {
                    }));
                    break;
                case Protocol.DBS_PIDS_SUPPORT_CODS:
                    pidsSupportCodsProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<Map<String, String>>>() {
                    }));
                    break;
                case Protocol.DBS_ENGINE_CUT:
                    engineCutProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<EngineCutEvent>>() {
                    }));
                    break;
                case Protocol.QUERY_RESPONSE:
//                    singleTlvProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<TlvEvent>>() {
//                    }));
                    commandResponseProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<CommandEvent>>() {
                    }));
                    break;
                case Protocol.NET_CONFIG_STATUS:
                    netConfigStatusProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<NetConfigEvent>>() {
                    }));
                    break;
                case Protocol.TCP_UPGRADE_NOTIFY:
                    tcpUpgradeNotifyProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<TCPUpgradeNotifyEvent>>() {
                    }));
                    break;
                case Protocol.TCP_UPGRADE_PROGRESS:
                    tcpUpgradeProgressProcess(JsonUtils.getObjectMapper().readValue(jsonStr, new TypeReference<EventMessage<TCPUpgradeProgressEvent>>() {
                    }));
                    break;

                default:
                    log.warn("The protocol ID has not been implemented: " + eventMessage.getProtocolId());
                    break;
            }

        } catch (IOException e) {
            log.error("Failed to handle OBD event." + e);
        }
    }

    private void travelProcess(EventMessage<?> eventMessage) {
        TravelDataEvent data = (TravelDataEvent) eventMessage.getData();
        String devId = eventMessage.getDevId();
        Map<String, String> map = lettuceService.hgetallSync(devId);
        DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);
        Map<String, String> statMap = new HashMap<>();
        if (data.getFlag() == 1) {
            statMap.put("tripNumber", String.valueOf(data.getTripNumber()));
        }
//        else if (data.getFlag() == 2){
//            //To determine if it's the same trip.
//            if (cache != null){
//
//                if (cache.getTripNumber() != null && data.getTripNumber() == cache.getTripNumber()) {
//                    if (cache.getTotalFuelConsu() != null){
//                        statMap.put("totalFuelConsu", String.valueOf(data.getTripFuel() * 0.01d + cache.getTotalFuelConsu()));
//                    }
//
//                    if (cache.getDeviceMileage() != null){
//                        statMap.put("deviceMileage",String.valueOf(data.getTripMileage() + cache.getDeviceMileage()));
//                    }
//                } else {
//                    log.warn("endTripEvent does not correspond to startTripEvent - endTime: {} devId: {}",data.getEndTime(),devId);
//                }
//
//                if (cache.getStartTripFuel() != null && cache.getStartTripMileage() != null) {
//                    String totalFuelConsu = statMap.get("totalFuelConsu");
//                    String deviceMileage = statMap.get("deviceMileage");
//
//                    if (totalFuelConsu != null){
//                        BigDecimal totalFuel = new BigDecimal(totalFuelConsu).multiply(new BigDecimal("100"));
//                        if (totalFuel.longValue() > cache.getStartTripFuel()){
//                            statMap.put("fuelWear",String.valueOf(totalFuel.longValue() - cache.getStartTripFuel()));
//                        }
//
//                    }
//                    if (deviceMileage != null && Long.parseLong(deviceMileage) > cache.getStartTripMileage()){
//                        statMap.put("tripMileage",String.valueOf(Long.parseLong(deviceMileage) - cache.getStartTripMileage()));
//                    }
//                }
//            }
//        }

        if (!statMap.isEmpty()) {
//            if (statMap.containsKey("fuelWear") && statMap.containsKey("tripMileage")){
//                tripDao.addTripToUpdateQueue((short) 1, null, data.getEndTime(), Long.parseLong(statMap.get("tripMileage")), Long.parseLong(statMap.get("fuelWear")), devId, acconTime);
//            }
            lettuceService.hmsetAsync(devId, statMap);
        }
    }

    private void httpFtpUpgradeResult(EventMessage<FotaHttpFtpResultEvent> readValue) {
        int confirmation = readValue.getData().getConfirmation();
        if (confirmation == FotaHttpFtpResultEvent.UPGRADE_RESULT_SUCCESS) {
            fotaDao.updateFotaResult(readValue.getDevId(), 4);
        } else if (confirmation == FotaHttpFtpResultEvent.UPGRADE_RESULT_FAIL) {
            fotaDao.updateFotaResult(readValue.getDevId(), 5);
        } else if (confirmation == FotaHttpFtpResultEvent.UPGRADE_RESULT_CANCEL) {
            fotaDao.updateFotaResult(readValue.getDevId(), 3);
        }
    }

    private void httpFtpUpgradeProgress(EventMessage<FotaHttpFtpDownloadProgressEvent> eventMessage) {
        FotaHttpFtpDownloadProgressEvent data = eventMessage.getData();
        fotaDao.updateFotaProgress(eventMessage.getDevId(), data.getDownloadProgress());
    }

    private void httpFtpUpgradeNotify(EventMessage<FotaHttpFtpNotificationEvent> readValue) {
        FotaHttpFtpNotificationEvent data = readValue.getData();

        if (data.getConfirmation() == FotaHttpFtpNotificationEvent.UPDATE_CONFIRMATION_SUCCESS) {
            fotaDao.updateFotaProgress(readValue.getDevId(), 0);
        } else if (data.getConfirmation() == FotaHttpFtpNotificationEvent.UPDATE_CONFIRMATION_FAIL) {
            //0:Inited,1:Notification,2:In progress,3:Canceled,4:Successful,5:Failed
            fotaDao.updateFotaResult(readValue.getDevId(), 5);
        }
    }

    private void customFotaProcess(EventMessage<CustomFotaEvent> eventMessage) {
        int action = eventMessage.getData().getAction();
        if (action == CustomFotaEvent.FOTA_START) {
            fotaDao.startFota(eventMessage.getDevId());
        } else if (action == CustomFotaEvent.FOTA_EXPIRE) {
            fotaDao.fotaExpire(eventMessage.getDevId());
        }
    }

    private void queryWorkModeProcess(EventMessage<QueryWorkModeEvent> eventMessage) {
        Integer workMode = eventMessage.getData().getWorkMode();
        if (workMode != null && (workMode == 0 || workMode == 1 || workMode == 2)) {
            deviceDao.updateWorkMode(eventMessage.getDevId(), workMode);
        }
    }


    private void tcpUpgradeProgressProcess(EventMessage<TCPUpgradeProgressEvent> event) {
        TCPUpgradeProgressEvent data = event.getData();

        fotaDao.updateTcpUpgradeProgress(event.getDevId(), data.getProgress());
    }

    private void tcpUpgradeNotifyProcess(EventMessage<TCPUpgradeNotifyEvent> eventMessage) {
        TCPUpgradeNotifyEvent data = eventMessage.getData();

        fotaDao.updateTcpUpgradeNotify(eventMessage.getDevId(), data.getSeq(), data.getStatus(), data.getProtocolType());
    }

    private void pidsSupportCodsProcess(EventMessage<Map<String, String>> eventMessage) {
        String supportCods = eventMessage.getData().get("pidsSupportCods");
        String devId = eventMessage.getDevId();

        deviceDao.updateSupportPids(devId, supportCods);
    }

    private void engineCutProcess(EventMessage<EngineCutEvent> eventMessage) {
        EngineCutEvent event = eventMessage.getData();

        if (event == null) {
            deviceDao.updateEngineCutStatus(eventMessage.getDevId(), null, null);
        } else {
            deviceDao.updateEngineCutStatus(eventMessage.getDevId(), event.getEngineCutStatus(), event.getEngineCutConfigStatus());
        }
    }

    private void singleTlvProcess(EventMessage<TlvEvent> eventMessage) {
        TlvEvent event = eventMessage.getData();
        tlvDao.saveOrUpdate(eventMessage.getDevId(), event.getCmdSeq(),
                event.getTlvTag(), event.getTlvValue());
    }

    private void commandResponseProcess(EventMessage<CommandEvent> eventMessage){
        CommandEvent event = eventMessage.getData();
        if (!com.amazonaws.util.StringUtils.hasValue(event.getCommandContent()))return;
        if (event.getCommandContent().contains("[VERSION]")){
            deviceDao.updateFW(eventMessage.getDevId(),event.getCommandContent());
            deviceDao.saveDevParameters(eventMessage.getDevId(),"VERSION",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("TIMER:")){
            deviceDao.saveDevParameters(eventMessage.getDevId(),"TIMER",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("OBD_TIMER")){
            deviceDao.saveDevParameters(eventMessage.getDevId(),"OBD_TIMER",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("SERVER,")){
            deviceDao.saveDevParameters(eventMessage.getDevId(),"SERVER",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("IMEI:")){
            deviceDao.saveDevParameters(eventMessage.getDevId(),"PARAM",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("Battery:")){
            //Battery:4.08V,NORMAL; EXBAT:12.99V; login mode:2;authentication:1;GPRS:Link Up; CSQ:31; LTE Signal Level:Strong; GPS:Successful positioning; SVS Used in fix:17(26); GPS Signal Level:25,25,32,36,22,27,31,25,34,33,26,27,33,30,37,35,35 ACC:ON; Defense:OFF;
            deviceDao.saveDevParameters(eventMessage.getDevId(),"STATUS",event.getCommandContent());
        }else if(event.getCommandContent().startsWith("OBDSET")){
            deviceDao.saveDevParameters(eventMessage.getDevId(),"OBDSET",event.getCommandContent());
            //engine cut:
            if(event.getCommandContent().startsWith("OBDSET:")){
                int currVal = Integer.parseInt(event.getCommandContent().split(":")[1]);
//                EngineCutStatus status = deviceDao.getengineCutStatus(event.getDevId());
//                if (status != null && status.getEngineCutStatus() != null && status.getEngineCutConfigStatus() != null){
                    //if (status.getEngineCutStatus() == currVal && status.getEngineCutConfigStatus() == 1){
                        deviceDao.updateEngineCutStatus(event.getDevId(),currVal == 1 ? 0 : 1,2);
                   //}
//                }
            }
        }else{
            log.warn("Unknown command : {}",JsonUtils.writeValueAsString(event));
        }
    }


    private void netConfigStatusProcess(EventMessage<NetConfigEvent> eventMessage) {
        NetConfigEvent data = eventMessage.getData();
        deviceDao.updateNetConfigStatus(eventMessage.getDevId(), data.getStatus());
    }

    private void vinCodeEventProcess(EventMessage<Map<String, String>> event) {
        String vinCode = event.getData().get("vinCode");
        String devId = event.getDevId();

        if (StringUtils.isNotBlank(vinCode) && vinCode.length() <= 100) {
            deviceDao.updateVinCode(devId, vinCode);
        } else {
            log.warn("Invalid vin code, device: [{}], vin code: {}", devId, vinCode);
        }
    }

    private void commonTlvEventProcess(EventMessage<?> eventMessage) {
        CommonTlvEvent commonTlvEvent = (CommonTlvEvent) eventMessage.getData();

        for (int i = 0; i < commonTlvEvent.getTlvTag().length; i++) {
            tlvDao.saveOrUpdate(eventMessage.getDevId(), commonTlvEvent.getCmdSeq(),
                    commonTlvEvent.getTlvTag()[i], commonTlvEvent.getTlvValues()[i]);
        }
    }

    private void upgradeProgressProcess(EventMessage<?> eventMessage) {
        UpgradeProgressEvent event = (UpgradeProgressEvent) eventMessage.getData();

        if (event.getReceiveMark() != null && event.getReceiveMark() == 0x01) {
            if (event.getMessageIndex() != null) {
                fotaDao.updateFotaProgress(eventMessage.getDevId(), event.getMessageIndex());
            }
        } else {
            log.warn("Fota receive data fail, device id: " + eventMessage.getDevId());
        }
    }

    private void upgradeNotifyProcess(EventMessage<?> eventMessage) {
        UpgradeNotifyEvent event = (UpgradeNotifyEvent) eventMessage.getData();

        int status;
        if (event.getUpdateConfirmation() != null && event.getUpdateConfirmation() == Constants.HT_UPGRADE_RET.HT_SUCCESS) {
            status = 4;
        } else if (event.getUpdateConfirmation() != null && event.getUpdateConfirmation() == Constants.HT_UPGRADE_RET.HT_PROGRESS) {
            status = 2;
            log.warn("Fota canceled, device id: " + eventMessage.getDevId());
        } else if (event.getUpdateConfirmation() != null && event.getUpdateConfirmation() == Constants.HT_UPGRADE_RET.HT_CANCEL) {
            status = 3;
            log.warn("Fota canceled, device id: " + eventMessage.getDevId());
        } else {
            status = 5;
            if (event.getUpdateConfirmation() != null) {
                log.warn("Fota fail, device id: " + eventMessage.getDevId() + ", result: " + event.getUpdateConfirmation());
            } else {
                log.warn("Fota fail, unknown error, device id: " + eventMessage.getDevId());
            }
        }
        fotaDao.updateFotaResult(eventMessage.getDevId(), status);
    }

    // Batch insert in the middle to avoid too late insert.
    private void batchProcess() {
        tripDao.batchUpdate();
        deviceDao.batchUpdateDevBasicSetting();
        batteryDao.batchInsertBatteryLog();
        gpsDataDynamoDdbDao.batchInsertWithRetry();
        alarmDynamoDbDao.batchInsertWithRetry();

        if (!logoutDataList.isEmpty()) {
            tripDao.updateAfterLogout(logoutDataList);
            logoutDataList.clear();
        }

        // send the SQS message the last. Previous data(gps, trip and so on) must be sent first.
        sqsService.sendBatchMessageToSQS();
//        if (!Env.getStage().equalsIgnoreCase("prod"))
//        	sqsAlertNotification.sendBatchMessageToSQS();
        fmcsaService.sendBatchMessageToSQS();
    }

    private void faultPassengerProcess(EventMessage<?> eventMessage) {
        DtcEvent dtcEvent = (DtcEvent) eventMessage.getData();

        if (dtcEvent.getDtcs().size() == 0) {
            // GW producer should not send the dtcEvent if the count is 0.
            log.error("faultPassengerProcess - GW producer sent 0 dtc event.");
            return;
        }

        String devId = eventMessage.getDevId();

        Map<String, String> map = lettuceService.hgetallSync(devId);
        DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);
        if (cache != null || cache.getAcconTime() == null) return;

        long acconTime = cache.getAcconTime();
        long tmTime = dtcEvent.getTmTime();

        // TODO check as batch.
        for (DtcEvent.DtcDetail detail : dtcEvent.getDtcs()) {

            if (detail.getCode() > 999999) {
                log.warn("faultPassengerProcess dtcCode >= 999999. devId:" + devId + " dtcCode:" + detail.getCode() + " accon_time:" + acconTime + " tmTime:" + tmTime);
                continue;
            }

//            if (alarmDao.isDuplicatedDtc(devId, dtcCode, DtcCarType.PASSENGER)) {
//                log.info("DTC DATA duplicattion. devId: " + devId + " dtcCode: " + dtcCode);
//                continue;
//            }

            // david FF-4087 [GW Consumer] Filter out DTCs that have no descriptions from DTC lookup table.
            if (!DtcCodeCache.cache.exist(DtcCarType.PASSENGER, detail.getCode())) {
                log.info("DTC DATA unsupported dtc type. devId: " + devId + " dtcCode: " + detail.getCode());
                continue;
            }

            log.info("faultPassenger: inserting to DTC_DATA: devId:" + devId + " dtcCode:" + detail.getCode() + " accon_time:" + acconTime + " tmTime:" + tmTime);
            DtcData dtcData = new DtcData(devId, acconTime, tmTime, 1, DtcCarType.PASSENGER, detail.getCode());
            long dtcDataId = alarmDao.insertToDtcData(dtcData, (byte)detail.getFlag());

            log.info("faultPassenger: inserting to AlertNotification: dtcDataId: " + dtcDataId);
//            AlertNotificationDto<DtcData> alertNotificationDto = new AlertNotificationDto<>(AlertNotification.DTC_DATA, GATEWAY_ID, dtcData);
//            if (!Env.getStage().equalsIgnoreCase("prod"))
//            	sqsAlertNotification.addToMessageList(alertNotificationDto);

            //TODO deprecated, should be removed
            alarmDao.insertToAlarmNotification(AlertNotification.DTC_DATA, dtcDataId);
        }
    }

//    private void faultCommercialProcess(EventMessage<?> eventMessage) {
//        DtcEvent dtcEvent = (DtcEvent) eventMessage.getData();
//
//        if (dtcEvent.getDtcCodeCount() <= 0) {
//            // GW producer should not send the dtcEvent if the count is 0.
//            log.warn("faultCommercialProcess - GW producer sent 0 dtc event.");
//            return;
//        }
//
//        String devId = eventMessage.getDevId();
//        Stat stat = dtcEvent.getStat();
//
//        for (long dtcCode : dtcEvent.getDtcCodeValues()) {
//            long tmTime = stat.getDeviceTime();
//
//            if (dtcCode > Integer.MAX_VALUE) {
//                log.warn("faultCommercialProcess dtcCode >= Integer.MAX_VALUE. devId:" + devId + " dtcCode:" + dtcCode + " accon_time:" + stat.getAcconTime() + " tmTime:" + tmTime);
//                continue;
//            }
//
//            /**
//             * protocol 6.6.2.2 0x400B
//             *
//             * dtc_array U32[x]:
//             *      Each DTC occupies 4 bytes, the first two bytes
//             *      represent DTC code, the 3rd byte represents
//             *      property of DTC, the 4th byte represents
//             *      occurrence count
//             *      <DTC_H> <DTC_L> <DTC_attr>
//             *      <DTC_OccurrenceCount>
//             */
//            long realDtcCode = dtcCode & 0x0000FFFF;
//
//            String devFWVersion = deviceDao.getDevFWVersion(devId);
//            //Gets the firmware name from the environment variable
//            String fwName = System.getenv("FwName");
//            if (!StringUtil.isNullOrEmpty(devFWVersion) && devFWVersion.toUpperCase().contains(fwName)) {
//                long dtcAttrCode = (dtcCode & 0x00FF0000) >> 16;
//                String realDtcBinary = ConvertUtil.transformToBinary(realDtcCode, 16);
//                String dtcAttrBinary = ConvertUtil.transformToBinary(dtcAttrCode, 8);
//                if (!dtcAttrBinary.isEmpty() && !realDtcBinary.isEmpty()) {
//                    String attrFiveToSev = dtcAttrBinary.substring(0, 3);
//                    String newDtcCode = attrFiveToSev.concat(realDtcBinary);
//                    realDtcCode = ConvertUtil.binary2Decimal(newDtcCode);
//                    log.info("The new DtcCode is  dtcCode: " + realDtcCode + " devId: " + devId);
//                }
//            }
//
//            if (dtcCode > 0 && !alarmDao.isDuplicatedDtc(devId, realDtcCode, DtcCarType.COMMERCIAL)) {
//                // david FF-4087 [GW Consumer] Filter out DTCs that have no descriptions from DTC lookup table.
//                if (!dtcService.exist(DtcCarType.COMMERCIAL, (int) dtcCode, "", devFWVersion)) {
//                    log.info("DTC DATA unsupported dtc type. devId: " + devId + " dtcCode: " + dtcCode);
//                    continue;
//                }
//
//                log.info("faultCommercialProcess: inserting to DTC_DATA: devId:" + devId + " dtcCode:" + dtcCode + " accon_time:" + stat.getAcconTime() + " tmTime:" + tmTime);
//                long happenTimes = (dtcCode & 0xFF000000) >> 24;
//                DtcData dtcData = new DtcData(devId, stat.getAcconTime(), tmTime, (int) happenTimes, DtcCarType.COMMERCIAL, dtcCode);
//                long dtcDataId = alarmDao.insertToDtcData(dtcData, dtcEvent.getDtcFlag());
//
//                log.info("faultCommercialProcess: inserting to AlertNotification: dtcDataId: " + dtcDataId);
////				AlertNotificationDto<DtcData> alertNotificationDto = new AlertNotificationDto<>(AlertNotification.DTC_DATA, GATEWAY_ID, dtcData);
////				if (!Env.getStage().equalsIgnoreCase("prod"))
////					sqsAlertNotification.addToMessageList(alertNotificationDto);
//
//                //TODO deprecated, should be removed
//                alarmDao.insertToAlarmNotification(AlertNotification.DTC_DATA, dtcDataId);
//            } else {
//                /*
//                 * Why do we store the data to dtc_data_commercial table?
//                 * Just to keep the log.
//                 */
//
//                // TODO try a batch processing later. Or store it to DynamoDB since RDS does not use this.
//                log.info("faultCommercialProcess: inserting to DTC_DATA_COMMERCIAL: devId:" + devId + " dtcCode:" + dtcCode + " accon_time:" + stat.getAcconTime() + " tmTime:" + tmTime);
//                alarmDao.insertToDtcDataCommercial(devId, stat.getAcconTime(), tmTime, dtcEvent.getDtcCodeValues().length, DtcCarType.COMMERCIAL, dtcCode);
//            }
//
//        }
//    }

    /**
     * SP-1037 Refine the fuel wear formula
     *
     * @param prevFw
     * @param prevMileage
     * @param prevTmTime
     * @param curFw
     * @param curMileage
     * @param curTmTime
     * @return
     */
    private int getActualFuel(Integer prevFw, Long prevMileage, Long prevTmTime, Integer curFw, Long curMileage,
                              Long curTmTime) {
        int tmp = curFw + FUEL_MAX * (prevFw / FUEL_MAX);
        if (tmp < prevFw) {
            tmp += FUEL_MAX;
        }

        int fuelDiff = tmp - prevFw; // 0.01L
        long distance = curMileage - prevMileage; // meter

        double kpl = fuelDiff > 0 ? (double) distance / (fuelDiff * 10) : 0; // km per liter

        if (distance > 0 && kpl < MAX_KPL) {
            return tmp;
        } else if (distance == 0 && fuelDiff > 0) {
            long timeDiffInSeconds = DateTimeUtils.differenceInSeconds(prevTmTime, curTmTime, DateTimeUtils.DTF_yyyyMMddHHmmss);
            if (fuelDiff < timeDiffInSeconds * MAX_IDLING_FW)
                return tmp;
        }

        return prevFw;
    }

    /**
     * Process {@link Stat} data in {@link GpsEvent}
     */
    private void statDataProcess(String devId, GpsEvent gpsEvent, DashboardCache prevCache, int[] gpsBound) {
        long start = System.currentTimeMillis();

        Gps[] gps = gpsEvent.getGps();
        int[] rpm = gpsEvent.getRpm();
        Stat stat = gpsEvent.getStat();
        Long deviceTime = stat.getDeviceTime();

        Gps startGps = gps[gpsBound[0]];
        long startTime = startGps.getTmTime();
        Gps lastGps = gps[gpsBound[1]];
        Long endTime = lastGps.getTmTime();
        Long prevTmTime = null;

        boolean isBatch = gps.length > 1;
        boolean updateDashboard = prevCache == null || prevCache.getTmTime() == null || endTime > prevCache.getTmTime();


        // Added Gps and RPM to Redis
        DashboardCache saveToRedis = DashboardCache.builder()
                .gatewayId(GATEWAY_ID)
                .acconTime(stat.getAcconTime())
                .deviceTime(deviceTime)
//                .tripMileage(stat.getCurrentTripMileage())
//                .fuelWear(stat.getFuelWear())
                .deviceMileage(stat.getTotalTripMileage())
                .totalFuelConsu(stat.getTotalFuel() * 0.01d)
                .dupCount(gpsBound[2])
                .lastGpsUpdateTime(DateTimeUtils.getUTCNowLong(DateTimeUtils.DTF_yyyyMMddHHmmss))
                .tmTime(lastGps.getTmTime())
                .direction(lastGps.getDirection())
                .lat(lastGps.getLatitude())
                .lng(lastGps.getLongitude())
                .speed(lastGps.getSpeed())
                .rpm(rpm[gpsBound[1]])
                .status(1) // online
                .build();

        if (gpsEvent.getTripNumber() != 0){
            saveToRedis.setTripNumber(gpsEvent.getTripNumber());
        }

        if (stat.getBatteryVoltage() > 0){
            saveToRedis.setBatteryVoltage(stat.getBatteryVoltage());
        }

        if (stat.getEngineHour() != null){
            saveToRedis.setTotalEngineHour(stat.getEngineHour());
        }

        try{
            long tripMileage = 0;
            if (prevCache.getStartTripMileage() != null && stat.getTotalTripMileage() != 0){
                if (stat.getTotalTripMileage()- prevCache.getStartTripMileage() < 0){
                    tripMileage = stat.getTotalTripMileage();
                }
                if (stat.getTotalTripMileage()- prevCache.getStartTripMileage() > 0){
                    tripMileage = stat.getTotalTripMileage()- prevCache.getStartTripMileage();
                }
            }else if(stat.getOdometer() != null){
                long mileage = (long)(Double.parseDouble(stat.getOdometer()) - prevCache.getOdometer()) * 1000;
                if (mileage > 0 && prevCache.getTripMileage() != null){
                    tripMileage = mileage + prevCache.getTripMileage().longValue();
                    if (log.isDebugEnabled()) log.debug("trip mileage from odometer : {}",tripMileage);
                }
            }
            long tripFuel = 0;
            if (prevCache.getStartTripFuel() != null && stat.getTotalFuel() != 0 ){
                if(stat.getTotalFuel() - prevCache.getStartTripFuel() < 0){
                    tripFuel = stat.getTotalFuel();
                }
                if (stat.getTotalFuel() - prevCache.getStartTripFuel() > 0){
                    tripFuel = stat.getTotalFuel()- prevCache.getStartTripFuel();
                }
            }
            if(log.isDebugEnabled()){
                log.debug("---- redis : {}",lettuceService.hgetallSync(devId));

                log.debug("----devId : {} ----- acconTime : {} ----- totalTripMileage : {} ----- totalFuel : {} ----------odometer:{}",
                        devId,stat.getAcconTime(),stat.getTotalTripMileage(),
                        stat.getTotalFuel() ,stat.getOdometer());
                log.debug("----devId : {} ----- acconTime : {} ----- startTripMileage : {} ----- startTripFuel : {} ----------pervOdometer:{}",
                        devId,stat.getAcconTime(), prevCache.getStartTripMileage(),
                        prevCache.getStartTripFuel(),prevCache.getOdometer());
            }
            log.info("----devId : {} ----- acconTime : {} ----- tripMileage : {} ----- tripFuel : {} ----------",
                    devId,stat.getAcconTime(),tripMileage, tripFuel);



            if (stat.getOdometer() != null){
                saveToRedis.setOdometer(Double.parseDouble(stat.getOdometer()));
            }

            if (tripMileage > 0 || tripFuel > 0){
                saveToRedis.setTripMileage(tripMileage);
                saveToRedis.setFuelWear(tripFuel);
                stat.setCurrentTripMileage(tripMileage);
                stat.setFuelWear(tripFuel);
                Trip trip = new Trip();
                trip.setAcconTime(stat.getAcconTime());
                trip.setDevId(devId);
                trip.setEndTime(stat.getDeviceTime());
                trip.setFuelWear(tripFuel);
                trip.setTripMileage(tripMileage);
                tripDao.addTirpMileageQueue(trip);
            }else{
                //update trip end time and end location
                Trip trip = new Trip();
                trip.setAcconTime(stat.getAcconTime());
                trip.setDevId(devId);
                trip.setEndTime(stat.getDeviceTime());
                trip.setEndLatLng(lastGps.getLatitude()+","+lastGps.getLongitude());
                tripDao.updateTripEndTime(trip);
            }

        }catch ( Exception e){
            System.err.println("update trip mileage error"+e.getMessage());
            //log.error("update trip mileage error",e);
        }

        // if trip exists.
        if (prevCache != null
                && prevCache.getAcconTime() != null
                && prevCache.getAcconTime().equals(stat.getAcconTime())) {
            // sometimes stat.deviceTime is older than the previous deviceTime. In this case, do not update trip.
            // before implementing it, let's see how many are there.
            // TODO based on the log, the difference should be calculated.
            if (prevCache.getDeviceTime() > deviceTime) {
                log.warn("New deviceTime is older than the previous deviceTime. devId:" + devId + ", acconTime:"
                        + stat.getAcconTime() + ", newDeviceTime:" + deviceTime + ", oldDeviceTime:" + prevCache.getDeviceTime());
                // return; // Don't do anything.
            }

            prevTmTime = prevCache.getTmTime();
            statDataDBProcess(TripEnum.REDIS, devId, stat, endTime,
                    prevCache.getFuelWear(), prevCache.getTripMileage(), prevCache.getEngineParamConfigStatus(), prevCache.getEndTime(),
                    saveToRedis, updateDashboard);

            if (prevCache.getEldTmTime() == null) {
                saveToRedis.setEldStatus((short) 0);
                saveToRedis.setEldCount((short) 0);
                saveToRedis.setEldTmTime(0L);
                saveToRedis.setEldIdleTmTime(0L);
                saveToRedis.setHasDriven((short) 0);
            } else {
                saveToRedis.setEldStatus(prevCache.getEldStatus());
                saveToRedis.setEldCount(prevCache.getEldCount());
                saveToRedis.setEldTmTime(prevCache.getEldTmTime());
                saveToRedis.setEldIdleTmTime(prevCache.getEldIdleTmTime());
                saveToRedis.setHasDriven(prevCache.getHasDriven());
            }

            if (updateDashboard) fmcsaService.processOngoingTrips(devId, saveToRedis, prevCache, isBatch);


        } else {
            //如果redis 中的 accon_time 和 stat 中的不同
            tripDBProcess(saveToRedis, prevCache, devId, stat, endTime, isBatch, startGps, updateDashboard);
        }

        // SP-1068 Keep the dashboard up-to-date
        if (updateDashboard) {
            log.info("Before sending to Redis for devId={}: {}", devId, JsonUtils.writeValueAsString(saveToRedis));

            // Save to Redis Dashboard
            lettuceService.hmsetAsync(devId, saveToRedis);
        }

        // Set trip dashboard
        lettuceService.setAsync(String.format(TTL_DEV_ACCON, devId, stat.getAcconTime()));
        // FF-5447 Set Hos History dashboard
        if (isBatch) {
            String hosHistoryKey = String.format(HOS_HISTORY_DEV_ACCON, devId, stat.getAcconTime());
            Map<String, String> hosHistoryMap = lettuceService.hgetallSync(hosHistoryKey);
            HosHistoryCache hosHistoryCache = hosHistoryMap != null
                    ? JsonUtils.getObjectMapper().convertValue(hosHistoryMap, HosHistoryCache.class)
                    : new HosHistoryCache();

            Long hosStartTime = hosHistoryCache.getStartTime() == null || startTime < hosHistoryCache.getStartTime()
                    ? startTime : hosHistoryCache.getStartTime();
            Long hosEndTime = hosHistoryCache.getEndTime() == null || endTime > hosHistoryCache.getEndTime()
                    ? endTime : hosHistoryCache.getEndTime();

            hosHistoryCache.setStartTime(hosStartTime);
            hosHistoryCache.setEndTime(hosEndTime);

            lettuceService.hmsetAsync(hosHistoryKey, hosHistoryCache);
            log.info("Set HOS History Cache: Key={}, Value={}", hosHistoryKey, JsonUtils.writeValueAsString(hosHistoryCache));
        }
        // Set Live ETA dashboard
        if (isBatch || prevCache == null || (prevCache.getTmTime() != null && endTime < prevCache.getTmTime())) {
            String liveEtaDevKey = String.format(LIVE_ETA_DEV, devId);
            lettuceService.updateLivaEtaAsync(liveEtaDevKey, stat.getAcconTime(), startTime);
        } else if (prevTmTime != null && prevCache.getLastGpsUpdateTime() != null
                && DateTimeUtils.differenceInSeconds(Long.toString(prevCache.getLastGpsUpdateTime()),
                DateTimeUtils.getUTCNow(DateTimeUtils.DTF_yyyyMMddHHmmss), DateTimeUtils.DTF_yyyyMMddHHmmss) < 5) {
            String liveEtaDevKey = String.format(LIVE_ETA_DEV, devId);
            lettuceService.updateLivaEtaAsync(liveEtaDevKey, stat.getAcconTime(), prevTmTime);
        }

        log.info("StatDataProcess: " + (System.currentTimeMillis() - start) + "ms");
    }

    private int convert2FleetUpSignal(int gsmSignal) {
        if (gsmSignal == 99) {
            return Constants.GSM_SIGNAL_LEVEL.GSM_NO_SIGNAL;
        } else if (gsmSignal <= 9) {
            return Constants.GSM_SIGNAL_LEVEL.GSM_EXTREMELY_WEAK;
        } else if (gsmSignal <= 14) {
            return Constants.GSM_SIGNAL_LEVEL.GSM_VERY_WEAK;
        } else if (gsmSignal <= 19) {
            return Constants.GSM_SIGNAL_LEVEL.GSM_GOOD;
        } else if (gsmSignal <= 31) {
            return Constants.GSM_SIGNAL_LEVEL.GSM_STRONG;
        }

        return -1;
    }

    /**
     * Check if the trip exist in DB
     *
     * @param saveToRedis
     * @param prevCache
     * @param devId
     * @param stat
     * @param endTime
     * @param isBatch
     * @param startGps
     */
    private void tripDBProcess(DashboardCache saveToRedis, DashboardCache prevCache, String devId, Stat stat, Long endTime,
                               boolean isBatch, Gps startGps, boolean updateDashboard) {
        Trip tripDBS = tripDao.getTripStatData(devId, stat.getAcconTime());
        if (prevCache == null) saveToRedis.setStatus(1); // online
        saveToRedis.setIfta(tripDBS.getIfta() == null ? 0 : tripDBS.getIfta()); // To handle NullPointerException when GPS event is processed before Login event for a new device.

        // startTime, endTime, and fuelWear should be 0 if the trip does not exist.
        if (tripDBS.getStartTime() != null && tripDBS.getStartTime() > 0) {
            statDataDBProcess(TripEnum.DB, devId, stat, endTime,
                    tripDBS.getFuelWear(), tripDBS.getTripMileage(), tripDBS.getEngineParamConfigStatus().intValue(), tripDBS.getEndTime(),
                    saveToRedis, updateDashboard);
        } else {
            double odometer = 0;
            long tripMileage = 0L;

            if (prevCache != null) {
                if (prevCache.getOdometer() != null) odometer = prevCache.getOdometer();
                if (prevCache.getTripMileage() != null) tripMileage = prevCache.getTripMileage();

                // SP-1542 Fixed a design flaw on trip updater.
                if (prevCache.getTmTime() != null && endTime > prevCache.getTmTime()
                        && prevCache.getStatus() != null && prevCache.getStatus() != 2) {
                    log.info("[tripDBProcess] Add event to TRIP update queue. dev_id: " + devId + ", currentTripMileage: " + stat.getCurrentTripMileage());
                    log.info("update trip : {}",Jackson.toJsonString(prevCache));
                    log.info("Event Stat : {}",Jackson.toJsonString(stat));
                    tripDao.addTripToUpdateQueue((short) 1, null, prevCache.getTmTime(), prevCache.getTripMileage(), prevCache.getFuelWear(), devId, prevCache.getAcconTime());

                    // Set trip dashboard
                    lettuceService.setAsync(String.format(TTL_DEV_ACCON, devId, prevCache.getAcconTime()));
                }
            }

            saveToRedis.setOdometer(odometer);
            saveToRedis.setFuelWear(stat.getFuelWear());

            String endTimeStr = null;

            saveToRedis.setStartTime(stat.getAcconTime());

            if (endTime != null) {
                saveToRedis.setEndTime(endTime);
                endTimeStr = String.valueOf(endTime);
            }

            log.info("Trip does not exist. devId=" + devId + ", acconTime=" + stat.getAcconTime() + ", startTime=" + stat.getAcconTime() + ", currentTripMileage=" + stat.getCurrentTripMileage() + ", currentFuel=" + stat.getFuelWear()
                    + ", odometer=" + odometer + ", deviceMileage=" + stat.getTotalTripMileage() + ", previousTripMileage=" + tripMileage);
            log.info("stat : "+ Jackson.toJsonPrettyString(stat));
            if (tripDao.insertToTrip(devId, stat.getAcconTime(), Long.toString(stat.getAcconTime()), endTimeStr,
                    stat.getCurrentTripMileage(), (int) stat.getFuelWear(), 1, odometer, stat.getTotalTripMileage())) {
                saveToRedis.setStartTripMileage(stat.getTotalTripMileage());
                saveToRedis.setStartTripFuel(stat.getTotalFuel());
                sqsService.addToMessageList(devId, String.valueOf(stat.getAcconTime()), "0", "1", "1-1");
                fmcsaService.startNewTrip(devId, saveToRedis, isBatch, startGps);
            }
        }
    }

    /**
     * The maximum value held by the device for fuel consumption for a trip is 65535.  This is defined as FUEL_MAX.
     * The latest firmware will cycle back to 0 liters consumed when crossing over the number 65535 for max fuel.
     * We want our database to store numbers beyond 65535 and keep counting up when the device cycles back to 0.
     * <p>
     * As a note, the new maximum value is still 999999 Centiliters because the field in the database can only store 6 digits, I am unsure what happens if this max is met.
     * <p>
     * These are the definitions and calculations for FuelActual (The actual total fuel consumed when using the OBD fuel consumed as input):
     * stat_data.m_ucurrent_fuel is the fuel number from the OBD device, 65535*(FuelDB/65535) processes the number correctly after rollovers,
     * and the third term is the logic to add 65535 centiliters for the rollover when it happens (OBD goes from 65530 fuel to 12 fuel, for example)
     * <p>
     * Adding an arbitrary 30000 to the comparison algorithm to deal with small fuel disrepencies that caused unwarranted rollovers.
     * Writelog for possible false positive rollover
     *
     * @param alias
     * @param devId
     * @param stat
     * @param lastTmTime
     * @param prevFuelWear
     * @param prevTripMileage
     * @param engineParamConfigStatus
     * @param prevEndTime
     * @param saveToRedis
     */
    private void statDataDBProcess(TripEnum alias, String devId, Stat stat, Long lastTmTime,
                                   Long prevFuelWear, Long prevTripMileage, Integer engineParamConfigStatus, Long prevEndTime,
                                   DashboardCache saveToRedis, boolean updateDashboard) {

//        int fuelActual = getActualFuel(prevFuelWear, prevTripMileage, prevEndTime, stat.getFuelWear(), stat.getCurrentTripMileage(), lastTmTime);
        if (lastTmTime != null
                && (prevEndTime == null || prevEndTime < lastTmTime)) {
            saveToRedis.setEndTime(lastTmTime);
        } else {
            saveToRedis.setEndTime(prevEndTime);
            lastTmTime = null;
        }

        saveToRedis.setFuelWear(stat.getFuelWear());
        saveToRedis.setEngineParamConfigStatus(engineParamConfigStatus);

        log.debug("[StatDataProcess " + alias + "] dev_id: " + devId + ", accon_time: " + stat.getAcconTime() + ", engineParamConfigStatus: " + engineParamConfigStatus + ", lastTmTime: " + lastTmTime);
        if (alias == TripEnum.DB) {
            log.info("[StatDataProcess " + alias + "] Add event to TRIP update queue. dev_id: " + devId + ", currentTripMileage: " + stat.getCurrentTripMileage() + ", fuelActual: " + stat.getFuelWear()
                    + ", prevTripMileage: " + prevTripMileage + ", prevFuelWear: " + prevFuelWear + ", prevEndTime: " + prevEndTime);
            log.debug("dashboard : {}",Jackson.toJsonString(saveToRedis));
            log.debug("EventStat : {}",Jackson.toJsonString(stat));
            tripDao.addTripToUpdateQueue((short) 1, null, lastTmTime, stat.getCurrentTripMileage(), stat.getFuelWear(), devId, stat.getAcconTime());
        }

        if (updateDashboard && lastTmTime != null) {
            DevBasicSetting devBasicSetting = DevBasicSetting.builder()
                    .devId(devId)
                    .lastUpdate(DateTimeUtils.getTimestamp(lastTmTime))
                    .build();
            if (stat.getOdometer() != null){
                devBasicSetting.setOdometer(Float.parseFloat(stat.getOdometer()));
            }

            if (stat.getEngineHour() != null){
                devBasicSetting.setTotalEngineHour(Float.parseFloat(stat.getEngineHour()));
            }

            if (stat.getBatteryVoltage() > 0){
                devBasicSetting.setBatteryVoltage(String.valueOf(stat.getBatteryVoltage()));
            }

            if (engineParamConfigStatus != null && engineParamConfigStatus == 1) { // what is 1?
                deviceDao.addToDevBasicSettingBatch(devBasicSetting);
            } else {
                double totalFuel = stat.getTotalFuel() * 0.01d;
                devBasicSetting.setTotalMileage(stat.getTotalTripMileage());
                devBasicSetting.setTotalFuelConsu(totalFuel);

                if (stat.getReserve() != null && stat.getReserve().length == 8) {
                    int gsmSignal = stat.getReserve()[4];
                    int fleetUpSignal = convert2FleetUpSignal(gsmSignal);
                    if (fleetUpSignal != -1) {
                        saveToRedis.setGsmSignal(fleetUpSignal);
                        devBasicSetting.setGsmSignal(fleetUpSignal);
                        deviceDao.addToDevBasicSettingBatch(devBasicSetting);
                    } else {
                        deviceDao.addToDevBasicSettingBatch(devBasicSetting);
                    }
                } else {
                    deviceDao.addToDevBasicSettingBatch(devBasicSetting);
                }
            }
        }
    }


    /**
     * @return latest Gps's index in array.
     */
    private int[] gpsProcess(String devId, GpsEvent gpsEvent, DashboardCache prevCache) {
        int[] gpsBound = new int[3]; // 0: earilest tmTime, 1: latest tmTime, 2: duplicated count
        Arrays.fill(gpsBound, -1);

        // For OCD2.
        if (gpsEvent == null || gpsEvent.getGps() == null || gpsEvent.getGps().length <= 0) {
            log.warn("gpsProcess gpsEvent is null, empty or 0 size.");
            return gpsBound;
        }

        Gps[] gps = gpsEvent.getGps();
        int[] rpm = gpsEvent.getRpm();
        Stat stat = gpsEvent.getStat();

        // do batch process or not. think more. => Let's implement the batch first since inserting one at a time does not make any sense.

        // batch insert into gps_data table.
        // Oracle limit: https://docs.oracle.com/cd/B19306_01/server.102/b14200/statements_9014.htm
        long start = System.currentTimeMillis();

        long earliestTmTime = Long.MAX_VALUE;
        long latestTmTime = Long.MIN_VALUE;
        long pastTime = DateTimeUtils.getPastUTC(6, DateTimeUtils.DTF_yyyyMMddHHmmss);
        long futureTime = DateTimeUtils.getFutureUTC(1, DateTimeUtils.DTF_yyyyMMddHHmmss);

        int duplicatedCount = prevCache != null && prevCache.getDupCount() != null ? prevCache.getDupCount() : 0;
        long cachedTmTime = prevCache != null && prevCache.getTmTime() != null ? prevCache.getTmTime() - duplicatedCount : 0;
        boolean sameAsCached = prevCache != null && cachedTmTime == gps[0].getTmTime();
        gpsBound[2] = sameAsCached ? duplicatedCount : 0; // Used to increment tmTime and store into DynamoDB

        for (int i = 0; i < gps.length; i++) {
            long tmTime = gps[i].getTmTime();

            if (DateTimeUtils.isValid(tmTime, pastTime, futureTime)) {

                long differenceInSeconds = 0;
                if (prevCache != null){
                    if (prevCache.getDeviceMileage() != null && prevCache.getDeviceMileage() > stat.getTotalTripMileage()){
                        stat.setTotalTripMileage(prevCache.getDeviceMileage());
                    }

                    if (prevCache.getTotalFuelConsu() != null && prevCache.getTotalFuelConsu() > stat.getTotalFuel()){
                        stat.setTotalFuel((long)(prevCache.getTotalFuelConsu() * 100));
                    }

                    if (rpm[i] == 0 && gps[i].getSpeed() != 0 && prevCache.getRpm() != null){
                        rpm[i] = prevCache.getRpm();
                    }

                    if (prevCache.getTripNumber() != null){
                        long tripNumber = prevCache.getTripNumber();

                        if (gpsEvent.getTripNumber() == 0){
                            gpsEvent.setTripNumber(tripNumber);
                        }
                        log.info("preTripNumber : {} nowTripNumber : {}",tripNumber,gpsEvent.getTripNumber());
                        if (gpsEvent.getTripNumber() == tripNumber){

                            Long lastTmTime = prevCache.getTmTime();
                            differenceInSeconds = DateTimeUtils.differenceInSeconds(lastTmTime,stat.getDeviceTime(),DateTimeUtils.DTF_yyyyMMddHHmmss);
                            log.info("differenceInSeconds : {}",differenceInSeconds);
                            if (differenceInSeconds >= 0 && differenceInSeconds < END_TRIP_THRESHOLD*60){
                                stat.setAcconTime(prevCache.getAcconTime());
                                //当前gps属于redis 中的trip , 并计算了trip中的里程和油耗
                                if (prevCache.getStartTripMileage() != null){
                                    Long startTripMileage = prevCache.getStartTripMileage();
                                    if (stat.getTotalTripMileage() > startTripMileage){
                                        stat.setCurrentTripMileage(stat.getTotalTripMileage() - startTripMileage);
                                    }
                                }
                                if (prevCache.getStartTripFuel() != null){
                                    Long startTripFuel = prevCache.getStartTripFuel();
                                    if (stat.getTotalFuel() - startTripFuel >= 0){
                                        stat.setFuelWear(stat.getTotalFuel() - startTripFuel);
                                    }
                                }
                            }
                            //以下逻辑是处理历史数据， 且属于当前trip， 无需计算里程和油耗
                            if (differenceInSeconds < 0 && prevCache.getAcconTime() < stat.getDeviceTime()){
                                stat.setAcconTime(prevCache.getAcconTime());
                            }
                        }
                    } else {
                        //没有tripNum的情况下计算accon_time  , 但是是否需要计算里程？
                        if (prevCache.getAcconTime() != null){
                            Long lastTmTime = prevCache.getTmTime();
                            differenceInSeconds = DateTimeUtils.differenceInSeconds(lastTmTime,stat.getDeviceTime(),DateTimeUtils.DTF_yyyyMMddHHmmss);
                            log.info("differenceInSeconds : {}",differenceInSeconds);
                            if (differenceInSeconds < END_TRIP_THRESHOLD*60){
                                stat.setAcconTime(prevCache.getAcconTime());
                            }
                        }
                    }
                }


                //分发数据到geofence
                try {
                    if (!GEOFENCE_DISABLED){
                        String record = JsonUtils.getObjectMapper().writeValueAsString(new EventMessage<>(Constants.Protocol.LOCATION_DATA, devId, gpsEvent));
                        log.info(JSON.toJSONString(record));
                        ByteBuffer dataBuf = ByteBuffer.wrap(record.getBytes(StandardCharsets.UTF_8));
                        kinesisThirdPartyProducer.addRecords(dataBuf);
                    }
                } catch (Exception e){
                    log.error("Failed to add Gps record");
                }

                // rpm.
                int rpmTmp = 0;
                if (0 < rpm.length && i < rpm.length) {
                    rpmTmp = rpm[i];
                }

//                if (!GPS_DISABLED)
//                    gpsDataDao.addGpsDataToInsertQueue(devId, stat, gps[i], rpmTmp, gpsEvent.getHistoryStatus());

                String odometerStr = lettuceService.hgetSync(devId, "odometer");
                double odometer = isDouble(odometerStr) ? Double.parseDouble(odometerStr) : 0;

                if (stat.getOdometer() != null){
                    odometer = Double.parseDouble(stat.getOdometer());
                }

                /**
                 * SP-1068 Duplicated tmTime correction algorithm
                 * Cannot check fuel wear since it is not stored on Redis dashboard.
                 * Will compare lat, lng, and mileage if needed
                 */
                if (((sameAsCached && gps.length == 1) || (gps.length > 1 && (i > 0 && gps[0].getTmTime() == tmTime)))
                        && (prevCache == null || rpm[i] != prevCache.getRpm() || gps[i].getSpeed() != prevCache.getSpeed()
                        || gps[i].getDirection() != prevCache.getDirection())) {

                    gpsBound[2]++;
                    tmTime = DateTimeUtils.addSecond(tmTime, gpsBound[2], DateTimeUtils.yyyyMMddHHmmss); // adding gpsBound[2] seconds.

                    log.warn(devId + " Update tmTime from " + gps[i].getTmTime() + " to " + tmTime + ", the current number of duplicates is " + gpsBound[2]);
                    gps[i].setTmTime(tmTime);
                } else {
                    gpsBound[2] = 0;
                }

                // To get the earliest Gps.
                if (earliestTmTime >= tmTime) {
                    earliestTmTime = tmTime;
                    gpsBound[0] = i;
                }

                // To get the latest Gps.
                if (latestTmTime <= tmTime) {
                    latestTmTime = tmTime;
                    gpsBound[1] = i;
                }

                gpsDataDynamoDdbDao.addToBatchList(devId, stat, gps[i], rpmTmp, gpsEvent.getHistoryStatus(), odometer);
                if (differenceInSeconds < 0 || stat.getDeviceTime() < stat.getAcconTime()) {
                    gpsBound[1] = -1;
                }
            } else {
                log.warn("tmTime is invalid. {\"devId\":\"" + devId + "\",\"acconTime\":" + stat.getAcconTime() + ",\"tmTime\":" + tmTime + ",\"pastTime\":" + pastTime + ",\"futureTime\":" + futureTime + "}");
            }
        }

        if (earliestTmTime != Long.MAX_VALUE && earliestTmTime != gps[0].getTmTime())
            log.warn("The 1st tmTime in GPS array [" + gps[0].getTmTime() + "] is not the earliest tmTime [" + earliestTmTime + "]");

        log.info("GpsDataProcess: " + (System.currentTimeMillis() - start) + "ms");

        return gpsBound;
    }

    /**
     * Check if a string be parsed into double.
     *
     * @param str
     * @return
     */
    private boolean isDouble(String str) {
        try {
            Double.parseDouble(str);
        } catch (NullPointerException | NumberFormatException e) {
            return false;
        }

        return true;
    }

    // TODO do batch processing as gpsProcess.
    private void conProcess(EventMessage<?> eventMessage) {
        PidEvent pidEvent = (PidEvent) eventMessage.getData();

        if (pidEvent == null || pidEvent.getPid() == null || pidEvent.getPid().size() <= 0) {
            log.warn("pidProcess pidEvent is null, empty or 0 size.");
            return;
        }

        String devId = eventMessage.getDevId();

        OldDevBasicSetting dbs = new OldDevBasicSetting();
        BatteryLog batteryLog = null;
        Map<String, String> map = lettuceService.hgetallSync(devId);
        DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);
        dbs.setDevId(devId);

        Map<String, String> statMap = new HashMap<>();
        DecimalFormat df = new DecimalFormat("#.####"); // '%.4f' in c++
        DecimalFormat singleDformat = new DecimalFormat("#.#");
        List<Pid> commercialPidList = new ArrayList<>();
        boolean lowResolutionOdometerExist = pidEvent.getPid().stream().anyMatch(p -> p.getPidType() == PID.TotalVehicleDistance);
        for (Pid pid : pidEvent.getPid()) {
            switch (pid.getPidType()) {
                case PID.EngineTotalHoursOfOperation:
                case PID.TotalEngineHours:
                    if (pid.getPidData() <= 0) { // This should not happen since producer filters this out.
                        log.warn("[conProcess] GW Producer sent EngineHours pid: " + pid + ", pidData:" + pid.getPidData());
                        break;
                    }
                    // sprintf(acSQL, "UPDATE DEV_BASIC_SETTING SET TOTAL_ENGINE_HOUR='%.4f' WHERE DEV_ID='%s'", fVecData, pData->obdID);
                    dbs.setTotalEngineHour(pid.getPidData());
                    statMap.put("totalEngineHour", df.format(pid.getPidData()));

                    break;
                case PID.TotalVehicleDistance:  //0x00F5
                case PID.TotalVehicleDistance2: //0x1F50
                    if (pid.getPidData() <= 0) {
                        log.warn("[conProcess] GW Producer sent Distance pid: " + pid + ", pidData:" + pid.getPidData());
                        break;
                    }
                    // sprintf(acSQL, "UPDATE DEV_BASIC_SETTING SET ODOMETER='%.4f' WHERE DEV_ID='%s'", fVecData, pData->obdID);
                    dbs.setOdometer(pid.getPidData());
                    statMap.put("odometer", df.format(pid.getPidData()));
                    break;
                case PID.ODOMETER_HIGH_RESOLUTION: //0x0395
                    if (lowResolutionOdometerExist) {
                        /**
                         * FF-5323
                         * If both Low-Resolution Odometer (0x00F5) AND High-Resolution Odometer (0x0395) PIDs are coming in at the same time, use the Low-Resolution Odometer (0x00F5).
                         * If ONLY Low-Resolution Odometer (0x00F5) PID is available, use the Low-Resolution Odometer (0x00F5) PID.
                         * If ONLY High-Resolution Odometer (0x0395) PID is available, use the High-Resolution Odometer (0x0395) PID.
                         */
                        // if there is a 0x00F5 exist, ignored 0x0395
                        break;
                    }
                    if (pid.getPidData() <= 0) {
                        log.warn("[conProcess] GW Producer sent Distance pid: " + pid + ", pidData:" + pid.getPidData());
                        break;
                    }
                    // sprintf(acSQL, "UPDATE DEV_BASIC_SETTING SET ODOMETER='%.4f' WHERE DEV_ID='%s'", fVecData, pData->obdID);
                    dbs.setOdometer(pid.getPidData());
                    statMap.put("odometer", df.format(pid.getPidData()));
                    break;
                case PID.FuelLevelInput:
                case PID.FuelLevel_A:
                case PID.FuelLevel_B:
                case PID.PASSENGER_FUEL_LEVEL:
                    if (pid.getPidData() > 100 || pid.getPidData() < 0) { // This should not happen since producer filters this out.
                        log.warn("[conProcess] GW Producer sent FuelLevel pid: " + pid + ", pidData:" + pid.getPidData());
                        break;
                    }
                    //sprintf(acSQL, "UPDATE DEV_BASIC_SETTING SET FUEL_LEVEL='%.0f' WHERE DEV_ID='%s'", fVecData, pData->obdID);
                    dbs.setFuelLevel((int) pid.getPidData());
                    statMap.put("fuelLevel", String.valueOf((int) pid.getPidData()));
                    break;
                case PID.SecondFuelLevel:
                    if (pid.getPidData() > 100 || pid.getPidData() < 0) { // This should not happen since producer filters this out.
                        log.warn("[conProcess] GW Producer sent SecondFuelLevel pid: " + pid + ", pidData:" + pid.getPidData());
                        break;
                    }
                    //sprintf(acSQL, "UPDATE DEV_BASIC_SETTING SET FUEL_LEVEL2='%.0f' WHERE DEV_ID='%s'", fVecData, pData->obdID);
                    dbs.setFuelLevel2((int) pid.getPidData());
                    statMap.put("fuelLevel2", String.valueOf((int) pid.getPidData()));
                    break;
                case PID.BATTERY_VOLTAGE:
                    dbs.setBatteryVoltage((int) pid.getPidData());
                    statMap.put("batteryVoltage", String.valueOf((int) pid.getPidData()));
                    break;

                case PID.COMMERCIAL_OIL_TEMPERATURE:
                case PID.PASSENGER_OIL_TEMPERATURE:
                    dbs.setOilTemperature((float) pid.getPidData());
                    statMap.put("oilTemperature", String.valueOf((int) pid.getPidData()));
                    break;

                case PID.ODOMETER_2020:
                    float odoInKm = pid.getPidData() * 0.1f;
                    dbs.setOdometer(odoInKm);
                    statMap.put("odometer", String.valueOf(odoInKm));
                    break;

                case PID.PASSENGER_ENGINE_REF_TORQUE:
                    dbs.setEngineRefTorque((float) pid.getPidData());
                    statMap.put("engineRefTorque", String.valueOf((int) pid.getPidData()));
                    break;

                case PID.PASSENGER_FUELRAIL_PRESSURE:
                    dbs.setFuelRailPressure(pid.getPidData());
                    statMap.put("fuelRailPressure", String.valueOf(pid.getPidData()));
                    break;

                case PID.EV_SOC: //EV_SOC.
                    String batteryLevel = String.valueOf(Math.round(pid.getPidData()));
                    if (cache != null) {
                        if (cache.getBatteryLevel() == null || !cache.getBatteryLevel().equals(batteryLevel) || pid.getAcconTime() != cache.getLastBatteryUpdateTime()) {
                            batteryLog = new BatteryLog();
                            batteryLog.setAcconTime(cache.getAcconTime());
                            batteryLog.setDevId(devId);
                            batteryLog.setTmTime(pid.getTmTime());
                            batteryLog.setLat(cache.getLat());
                            batteryLog.setLng(cache.getLng());
                            batteryLog.setBattery(Math.round(pid.getPidData()));
                            dbs.setBattery(Math.round(pid.getPidData()));
                        }
                    }
                    statMap.put("batteryLevel", batteryLevel);
                    statMap.put("lastBatteryUpdateTime", String.valueOf(pid.getAcconTime()));
                    break;
                case PID.EV_CHARGING_STATUS: //EV Charging status.
                    int isCharging = (int) pid.getPidData();
                    if (isCharging != 1) {
                        isCharging = 0;
                    }
                    if (batteryLog != null) {
                        batteryLog.setIsCharging(isCharging);
                    }
                    if (cache != null) {
                        if (cache.getIsCharging() == null || !cache.getIsCharging().equals(isCharging)) {
                            dbs.setIsCharging(isCharging);
                        }
                    }
                    statMap.put("isCharging", String.valueOf(isCharging));
                    break;
                case PID.EV_TRIP_MILEAGE: //EV vehicle range
                    if (cache != null) {
                        if (cache.getRemainingRange() == null || (Float.compare(cache.getRemainingRange(), pid.getPidData()) != 0)) {
                            dbs.setRemainingRange(pid.getPidData());
                        }
                    }

                    if (batteryLog != null) {
                        batteryLog.setRemainingRange(pid.getPidData());
                    }

                    statMap.put("remainingRange", String.valueOf(pid.getPidData()));
                    break;

                case PID.COMMERCIAL_OIL_TEMP: // Oil Temperature/DegC
                    pid.setPidCat(PidCategoryEnum.ENGINE_OIL_TEMPERATURE.getCode());
                    commercialPidList.add(pid);
                    statMap.put("oilTemperature", String.valueOf((int) pid.getPidData()));
                    break;
                case PID.COMMERCIAL_ENGINE_REF_TORQUE: // Engine Reference Torque/Newton Meter
                    pid.setPidCat(PidCategoryEnum.ENGINE_REFERENCE_TORQUE.getCode());
                    commercialPidList.add(pid);
                    statMap.put("engineRefTorque", String.valueOf((int) pid.getPidData()));
                    break;
                case PID.COMMERCIAL_RPM: //RPM (0x3103) Default IPD.
                    String rpm = String.valueOf((int) Float.intBitsToFloat((int) pid.getPidData()));
                    log.debug("0x3103 : " + rpm);
                    pid.setPidCat(PidCategoryEnum.ENGINE_SPEED.getCode());
                    pid.setPidDataStr(rpm);
                    commercialPidList.add(pid);
                    statMap.put("rpm", rpm);
                    break;
                case PID.COMMERCIAL_FUEL_LEVEL: // 0x3104: fuel level %. :
                    String fuelLevel = singleDformat.format(Float.intBitsToFloat((int) pid.getPidData()));
                    log.debug("0x3104 : " + fuelLevel);
                    pid.setPidDataStr(fuelLevel);
                    pid.setPidCat(PidCategoryEnum.FUEL_LEVEL.getCode());
                    commercialPidList.add(pid);
                    statMap.put("fuelLevel", fuelLevel);
                    break;
                case PID.COMMERCIAL_FUEL_RAIL_PRES: // 0x3105 : Fuel Rail Pressure/Psi
                    String fuelRailPressure = singleDformat.format(Float.intBitsToFloat((int) pid.getPidData()));
                    log.debug("0x3105 : " + fuelRailPressure);
                    pid.setPidDataStr(fuelRailPressure);
                    pid.setPidCat(PidCategoryEnum.FUEL_PRESSURE.getCode());
                    commercialPidList.add(pid);
                    statMap.put("fuelRailPressure", fuelRailPressure);
                    break;
                case PID.COMMERCIAL_ODOMETER: // 0x3106 Odometer
                    String odometer = singleDformat.format(pid.getPidData());
                    log.debug("0x3106 : " + odometer);
                    pid.setPidDataStr(odometer);
                    pid.setPidCat(PidCategoryEnum.ODOMETER.getCode());
                    commercialPidList.add(pid);
                    statMap.put("odometer", odometer);
                    break;
                case PID.COMMERCIAL_TOTAL_FUEL_CONS: // Total Fuel Consumption Trip Data
                    pid.setPidCat(PidCategoryEnum.TOTAL_FUEL_CONSUMPTION_TRIP_DATA.getCode());
                    commercialPidList.add(pid);
                    statMap.put("totalFuelConsumption", String.valueOf(pid.getPidData()));
                    break;
                case PID.COMMERCIAL_TOTAL_TRIP_TIME: // Total Time trip Data
                    pid.setPidCat(PidCategoryEnum.TOTAL_TIME_TRIP_DATA.getCode());
                    commercialPidList.add(pid);
                    statMap.put("totalTripTime", String.valueOf(pid.getPidData()));
                    break;
                case PID.COMMERCIAL_VSS: //VSS (8461) Default IPD.
                    pid.setPidCat(PidCategoryEnum.VEHICLE_SPEED_SENSOR.getCode());
                    commercialPidList.add(pid);
                    statMap.put("VSS", String.valueOf(pid.getPidData()));
                    break;
                default:
                    log.warn("[conProcess] GW Producer sent unsupported pid: " + pid + ", pidData:" + pid.getPidData());
            }
        } // for loop

        if (!statMap.isEmpty()) {
            lettuceService.hmsetAsync(devId, statMap);
        }
        if (batteryLog != null) {
            batteryDao.addInBatch(batteryLog);
        }
        deviceDao.updateDevBasicSetting(dbs.getTotalEngineHour(), dbs.getOdometer(), dbs.getFuelLevel(), dbs.getFuelLevel2(), dbs.getDevId(),
                dbs.getBatteryVoltage(),
                dbs.getOilTemperature(),
                dbs.getEngineRefTorque(),
                dbs.getFuelRailPressure(),
                dbs.getRemainingRange(),
                dbs.getBattery(),
                dbs.getIsCharging());
        if (commercialPidList.size() > 0) {
            //insert condata
            pidDataDao.insertConData(devId, commercialPidList);

        }
        log.debug("conProcess was done.");
    }

    private void alarmConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "ALARM_CONFIG_STATUS", statusCode);
    }

    private void workModeConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "OMMODE_CONFIG_STATUS", statusCode);
    }

    private void fuelParamConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "FUEL_PARAM_CONFIG_STATUS", statusCode);
    }

    private void synchUtcProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "SYNCH_UTC", statusCode);
    }

    private void powerOffdelayConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "PODELAY_CONFIG_STATUS", statusCode);
    }

    private void engineParamConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "ENGINE_PARAM_CONFIG_STATUS", statusCode);
        lettuceService.hsetAsync(event.getDevId(), "engineParamConfigStatus", String.valueOf(statusCode));
    }

    private void uploadConfigProcess(EventMessage<Map<String, Integer>> event) {
        int statusCode = (event.getData().get("statusCode") == HTRet.HT_SUCCESS) ? 2 : 3;
        deviceDao.updateDevBasicSettingStatus(event.getDevId(), "UPLOAD_CONFIG_STATUS", statusCode);
    }

    private void alarmProcess(EventMessage<?> eventMessage) {
        AlarmEvent alarmEvent = (AlarmEvent) eventMessage.getData();

        String devId = eventMessage.getDevId();
        Map<String, String> map = lettuceService.hgetallSync(devId);
        DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);
        if (cache != null && cache.getAcconTime() != null) {
            alarmEvent.getStat().setAcconTime(cache.getAcconTime());
        } else {
            log.warn("The acconTime is missing");
        }

        Alarm[] alarm = alarmEvent.getAlarm();
        Gps gps = alarmEvent.getGps();
        Stat stat = alarmEvent.getStat();

        for (Alarm value : alarm) {
            if (value.getAlarmType() == 0x0D) {
                long inMin = DateTimeUtils.differenceInSeconds(stat.getAcconTime(), stat.getDeviceTime(), DateTimeUtils.DTF_yyyyMMddHHmmss) / 60;
                if (inMin > 0) {
                    value.setAlarmDescription((int) inMin);
                }
            }

            boolean insertable = insertableAlarm(devId, value);
            if (!insertable) {
                log.debug("alarm: No insertion. alarmType:" + value.getAlarmType() + ", devId:" + devId);
                continue;
            }

            long iLat = 0;
            long iLon = 0;
            //if (pData->m_gpsdata.m_uGps_count > 0) {
            if (gps != null) { // is this really happening? -> Don't know. So just add this condition as in C++ GW.
                iLat = gps.getLatitude();
                iLon = gps.getLongitude();
            }

            if (!alarmTypesToFilter.contains(value.getAlarmType())) {
                AlarmData alarmData = alarmDynamoDbDao.addToBatchList(devId, stat.getAcconTime(), stat.getDeviceTime(),
                        value.getAlarmType(), value.getAlarmDescription(), iLat, iLon);

//                AlertNotificationDto<AlarmData> alertNotificationDto = new AlertNotificationDto<>(AlertNotification.ALARM_DATA, GATEWAY_ID, alarmData);
//                if (!Env.getStage().equalsIgnoreCase("prod"))
//                	sqsAlertNotification.addToMessageList(alertNotificationDto);

                //TODO: deprecated, should be removed.
                long alarmId = alarmDao.insertToAlarmData(devId, stat.getAcconTime(), stat.getDeviceTime(),
                        value.getAlarmType(), value.getAlarmDescription(), iLat, iLon);
                alarmDao.insertToAlarmNotification(AlertNotification.ALARM_DATA, alarmId);
                alarmDao.updateHosScorecardData(alarmData);
            }
        }
    }

    private boolean insertableAlarm(String devId, Alarm alarm) {
        int alarmType = alarm.getAlarmType();
        // 1: alarm triggered, new alarm  0: alarm eliminated, alarm end
        if (!alarm.isNewAlarm() && alarmType != AlarmType.HTAT_PARK_ACCON
                && alarmType != AlarmType.HTAT_FATIGUE_DRIVE
                && alarmType != AlarmType.HTAT_URGENCY) return false;
		
		/*
		// Commenting out hard braking and hard acceleration skips, so we get this data again for use with safety score
		alarmType == AlarmType.HTAT_HARD_ACC
				|| alarmType == AlarmType.HTAT_HARD_BROKE ||

		david FF-4076 save Engine On/Off Alarm
				|| alarmType == AlarmType.HTAT_ACC_OFF
                || alarmType == AlarmType.HTAT_ACC_ON
		*/
        if (alarmType == AlarmType.HTAT_DRAG
                || alarmType == AlarmType.HTAT_TAILGAS
                || alarmType == AlarmType.HTAT_MIL) return false;
		
		/*
		fix 715,716 for acct_id in (1502,1482,1542,1802,1803) - Samex, Adams & Sons, New Connect, RM Myers Corp, ATC
		Steve 1/27/2016 - Add ACCT_ID in (1721 = Hana Global, 3240 = JTOBAR)
		Steve, 02/02/2016, removed 1721 = Hana Global
		*/
        if (alarmType == AlarmType.HTAT_POWNON
                || alarmType == AlarmType.HTAT_CRASH_DRIVE
                || alarmType == AlarmType.HTAT_URGENCY
                || alarmType == AlarmType.HTAT_CRASH) {

            boolean isAlarmDisabledDevice = deviceDao.isAlarmDisabledDevice(devId);
            if (isAlarmDisabledDevice) return false;
        }

        //fix bug 1286 and bug 1337 to allow special panic button firmware "IDD_213G02_S_V2.3.3_TL_A_SOS.bin" to send end alarm with value "2"
        if (!alarm.isNewAlarm() && alarmType == AlarmType.HTAT_URGENCY) {
            alarm.setAlarmDescription(2); // what is 2? don't know. See C++ gateway.
        }

        if (alarmType == AlarmType.HTAT_WATER) {
            int m_ualarm_desc = alarm.getAlarmDescription() - 40;
            alarm.setAlarmDescription(m_ualarm_desc);
        }

        if (alarmType == AlarmType.HTAT_LOWVATT && alarm.getAlarmDescription() < 100) return false;
        if (alarmType == AlarmType.HTAT_WATER && (alarm.getAlarmDescription() > 110 || alarm.getAlarmDescription() < 0))
            return false;
		
		/* There should not be overspeed with 0 km/h.  Suppress Sino OBD device's wrong data. fix 710
		   20150916 - Fix Bug 1122 - Gateway: Block non-sense data from SINO's incorrect decoding of J1708 protocol.
		 */
        if (alarmType == AlarmType.HTAT_OVERSPEED
                && (alarm.getAlarmDescription() == 0 || alarm.getAlarmDescription() == 136
                || alarm.getAlarmDescription() == 137 || alarm.getAlarmDescription() >= 145)
        ) return false;

        // There should not be idling with 0 minute.  Suppress Sino OBD device's wrong data.
        if (alarmType == AlarmType.HTAT_PARK_ACCON && alarm.getAlarmDescription() == 0) return false;

        //fix 709
        if (alarmType == AlarmType.HTAT_RPM_HIGH && (alarm.getAlarmDescription() >= 5500 || alarm.getAlarmDescription() == 4186))
            return false;

        // Fix for bug #575 (Excessive driving alert with 17476 minutes)
        if (alarmType == AlarmType.HTAT_FATIGUE_DRIVE && alarm.getAlarmDescription() >= 17000) return false;

        return true;
    }

    private String getPidsSupportCods(LoginEvent loginEvent) {
        String result = null;
        int[] newParameterArray = loginEvent.getNewParams();
        if (newParameterArray == null) return null;

        result = Arrays.toString(newParameterArray).replaceAll("\\[|\\]|\\s+", "");
        return result;
    }

    private int getProtocalObd(int protocolObd) {
        if (protocolObd < 0 || protocolObd > 99)
            protocolObd = 0;
        return protocolObd;
    }

    private void loginProcess(EventMessage<?> eventMessage) {
        // check if device exists.
        // if not, insert into device and dev_basic_setting
        // "insert into DEVICE (DEV_ID) values('%s')"
        // "insert into DEV_BASIC_SETTING (DEV_ID, ON_LINE) values('%s', 1)"

        //boolean deviceExist = deviceService.deviceExist(eventMessage.getDevId());
        //boolean deviceExist = deviceDao.deviceExist(eventMessage.getDevId());
        DeviceSetting deviceSetting = new DeviceSetting();
        deviceSetting = deviceDao.deviceExist(eventMessage.getDevId());
        log.info("Device Details: " + deviceSetting.toString());
        if (StringUtils.isEmpty(deviceSetting.getDevId())) {
            log.debug("Adding Device entry in Device Table for dev_id: " + eventMessage.getDevId());
            Device device = new Device();
            device.setDevId(eventMessage.getDevId());
            deviceDao.insertToDevice(device);
        }

        if (StringUtils.isEmpty(deviceSetting.getDeviceSettingId())) {
            log.debug("Adding Device Setting entry in Device Basic Setting Table for dev_id: " + eventMessage.getDevId());
            OldDevBasicSetting devBasicSetting = new OldDevBasicSetting();
            devBasicSetting.setDevId(eventMessage.getDevId());
            devBasicSetting.setOnLine(1);
            deviceDao.insertToDevBasicSetting(devBasicSetting);
        }

        if (StringUtils.isEmpty(deviceSetting.getEldSettingId())) {
            log.debug("Adding ELD Setting entry in ELD Setting Table for dev_id: " + eventMessage.getDevId());
            OldDevBasicSetting devBasicSetting = new OldDevBasicSetting();
            devBasicSetting.setDevId(eventMessage.getDevId());
            devBasicSetting.setOnLine(1);
            deviceDao.insertToEldSetting(devBasicSetting);
        }

        // update DEV_BASIC_SETTING
        // "update DEV_BASIC_SETTING set PIDS_SUPPORT_CODS = '%s', DEV_FW_VER = '%s', DEV_HW_VER = '%s', PROTOCOL_FROM_OBD = %d where DEV_ID = '%s'",
        //acParam, acFWVersion, acHWVersion, protocolObd, pData->obdID);
        LoginEvent loginEvent = (LoginEvent) eventMessage.getData();
        //stat.getEngineDiagnoseProtocol();

        OldDevBasicSetting devBasicSetting = new OldDevBasicSetting();
        devBasicSetting.setDevId(eventMessage.getDevId());
//        devBasicSetting.setPidsSupportCods(getPidsSupportCods(loginEvent));
        devBasicSetting.setDevFwVer(loginEvent.getSoftwareVer());
        devBasicSetting.setDevHwVer(loginEvent.getHardwareVer());
        if (StringUtils.isEmpty(deviceSetting.getDevId())) {
            devBasicSetting.setProtocolFromObd(0);
        }
        deviceDao.updateDevBasicSetting(devBasicSetting);
		
		/* The following is not necessary since we are not sending any command to OBD. 
			 read device info. for what? to send a command.
			 "select d.vehicle_capacity,d.fuel_type,a.work_mode,a.gps_switch,a.gps_interval_sec,a.alarm_config_status,a.ommode_config_status,a.upload_config_status,
					a.fuel_param_config_status,a.synch_utc,a.engine_off_delay,a.podelay_config_status,a.protocol_from_obd,a.total_mileage,a.total_fuel_consu,a.engine_param_config_status,a.pids_switch 
					from DEVICE d 
					left join DEV_BASIC_SETTING a on d.dev_id=a.dev_id 
					where d.dev_id = '%s' and total_mileage between 0 and 4294967295",
					pData->obdID);
		*/

        // check if need to setting
//        Map<String, String> tlvMap = tlvDao.queryBootVoltageTlv(eventMessage.getDevId());
//        if (!tlvMap.isEmpty()) {
//            String s = buildMessageContent(eventMessage.getDevId(), tlvMap);
//            if (StringUtils.isNotBlank(s)) {
//                lettuceService.pushMessage(CHANNEL_NAME, s);
//            }
//        }
    }

    private String buildMessageContent(String devId, Map<String, String> tlvValues) {
        List<Map<String, Object>> tlvList = Lists.newArrayList();
        for (Map.Entry<String, String> entry : tlvValues.entrySet()) {
            Map<String, Object> tlv = new HashMap<>();
            tlv.put("tlvTag", entry.getKey());

            try {
                tlv.put("tlvValue", convertTlvValue(entry.getValue()));
                tlvList.add(tlv);
            } catch (Exception e) {
                log.error("convert tlv value failed. ", e);
            }
        }
        Map<String, Object> event = Maps.newHashMap();
        event.put("tlvs", tlvList);

        Map<String, Object> remoteCommand = Maps.newHashMap();
        remoteCommand.put("devId", devId);
        remoteCommand.put("protocolId", Protocol.SETTING);
        remoteCommand.put("data", JSON.toJSONString(event));
        return JSON.toJSONString(remoteCommand);
    }

    private String convertTlvValue(String value) {
        BigDecimal i = new BigDecimal(value);

        i = i.multiply(BigDecimal.TEN);

        int temp = i.intValue();

        byte b1 = (byte) (temp & 0xFF);
        byte b2 = (byte) ((temp >> 8) & 0xFF);

        return Hex.encodeHexString(new byte[]{b1, b2});
    }

    private void logoutProcess(EventMessage<?> eventMessage) {
        LogoutEvent logoutEvent = (LogoutEvent) eventMessage.getData();
        String devId = eventMessage.getDevId();
        Map<String, String> map = lettuceService.hgetallSync(devId);
        DashboardCache cache = JsonUtils.getObjectMapper().convertValue(map, DashboardCache.class);
        Stat stat = new Stat();
        if (cache != null) {
            if (cache.getTotalFuelConsu() != null) {
                stat.setTotalFuel((long) (cache.getTotalFuelConsu() * 100));
            }

            if (cache.getDeviceMileage() != null) {
                stat.setTotalTripMileage(cache.getDeviceMileage());
            }
        }
        logoutEvent.setStat(stat);
        logoutEvent.setDevId(devId);
        logoutDataList.add(logoutEvent);
        lettuceService.hsetAsync(eventMessage.getDevId(), "status", "2");
    }

}
