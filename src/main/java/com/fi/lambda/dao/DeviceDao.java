package com.fi.lambda.dao;

import java.sql.*;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.fi.lambda.model.*;
import com.fi.lambda.utils.ArrayUtils;
import com.fleetup.rds.ConnectionGettor;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class DeviceDao {
	
//	private static final String SQL_DBS_UPDATE_TO_ONLINE = "UPDATE DEV_BASIC_SETTING SET ON_LINE=1 WHERE DEV_ID=? AND ON_LINE<>1";
	private static final String SQL_DEV_BASIC_SETTING_UPDATE =
			"UPDATE DEV_BASIC_SETTING\n" +
			"SET ON_LINE = 1,\n" +
			"    TOTAL_MILEAGE = COALESCE(?, TOTAL_MILEAGE),\n" +
			"    TOTAL_FUEL_CONSU = COALESCE(?, TOTAL_FUEL_CONSU),\n" +
			"    GSM_SIGNAL = COALESCE(?, GSM_SIGNAL),\n" +
			"    ODOMETER = COALESCE(?, ODOMETER),\n" +
			"    TOTAL_ENGINE_HOUR = COALESCE(?, TOTAL_ENGINE_HOUR),\n" +
			"    CURR_BATTERY_VOLTAGE = COALESCE(?, CURR_BATTERY_VOLTAGE)\n" +
			"WHERE DEV_ID = ?";
	
//	Queue<String> queueForDBSOnline = new ConcurrentLinkedQueue<>();
	private final Map<String,DevBasicSetting> lastDBSs = new HashMap<>();
	
	public DeviceSetting deviceExist(String devId) {
		String deviceQuery = "select d.dev_id as DEV_ID, dbs.dev_id as DEV_SETTING_ID, es.device_id as ELD_SETTING_ID "
				+ "from device d "
				+ "full outer join dev_basic_setting dbs on dbs.dev_id = d.dev_id "
				+ "full outer join eld_setting es on es.device_id = ? "
				+ "where (d.dev_id =? or dbs.dev_id=? or  es.device_id=?) and rownum < 2";
		DeviceSetting deviceSetting=new DeviceSetting();
		try (PreparedStatement statement = ConnectionGettor.prepareStatement(deviceQuery)){
			statement.setString(1, devId);
			statement.setString(2, devId);
			statement.setString(3, devId);
			statement.setString(4, devId);
			ResultSet rs = statement.executeQuery();

			while (rs.next()) {
				String dev_id = rs.getString("DEV_ID");
				String dev_setting_id = rs.getString("DEV_SETTING_ID");
				String eld_setting_id = rs.getString("ELD_SETTING_ID");
				deviceSetting.setDevId(dev_id);
				deviceSetting.setDeviceSettingId(dev_setting_id);
				deviceSetting.setEldSettingId(eld_setting_id);
			}
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
		return deviceSetting;
	}
	
	public void insertToDevice(Device device) {
		String sql = "INSERT INTO DEVICE (DEV_ID, LICENSE ,DEVICE_TYPE ,INTERNAL_DEVICE_TYPE) VALUES (?, ?, 3, 28)";
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, device.getDevId());
			ps.setString(2, device.getDevId());

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void insertToDevBasicSetting(OldDevBasicSetting devBasicSetting) {
		String sql = "INSERT INTO DEV_BASIC_SETTING (DEV_ID, ON_LINE) values(?, ?)";
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devBasicSetting.getDevId());
			ps.setInt(2, devBasicSetting.getOnLine());

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}
	

	public void insertToEldSetting(OldDevBasicSetting devBasicSetting) {

		// sprintf(acSQL, "INSERT ALL INTO ELD_SETTING (device_id, type, preference) VALUES ('%s',1,1) INTO ELD_SETTING (device_id, type, preference) VALUES ('%s',2,0) INTO ELD_SETTING (device_id, type, preference) VALUES  ('%s',3,0) INTO ELD_SETTING (device_id, type, preference) VALUES ('%s',4,0) SELECT * FROM dual", pData->obdID, pData->obdID, pData->obdID, pData->obdID);
		String sql = "INSERT ALL "
			+ "INTO ELD_SETTING (DEVICE_ID, TYPE, PREFERENCE) VALUES (?,1,1) "
			+ "INTO ELD_SETTING (DEVICE_ID, TYPE, PREFERENCE) VALUES (?,2,0) "
			+ "INTO ELD_SETTING (DEVICE_ID, TYPE, PREFERENCE) VALUES (?,3,0) "
			+ "INTO ELD_SETTING (DEVICE_ID, TYPE, PREFERENCE) VALUES (?,4,0) "
			+ "SELECT * FROM DUAL"
		;
		
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devBasicSetting.getDevId());
			ps.setString(2, devBasicSetting.getDevId());
			ps.setString(3, devBasicSetting.getDevId());
			ps.setString(4, devBasicSetting.getDevId());

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}
	
	public void updateDevBasicSetting(OldDevBasicSetting devBasicSetting) {
		// update DEV_BASIC_SETTING
		// "update DEV_BASIC_SETTING set PIDS_SUPPORT_CODS = '%s', DEV_FW_VER = '%s', DEV_HW_VER = '%s', PROTOCOL_FROM_OBD = %d where DEV_ID = '%s'",
		//acParam, acFWVersion, acHWVersion, protocolObd, pData->obdID);
		String sql = "update DEV_BASIC_SETTING set PIDS_SUPPORT_CODS = ?, DEV_FW_VER = ?, DEV_HW_VER = ?, PROTOCOL_FROM_OBD = ? where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devBasicSetting.getPidsSupportCods());
			ps.setString(2, devBasicSetting.getDevFwVer());
			ps.setString(3, devBasicSetting.getDevHwVer());
			ps.setInt(4, devBasicSetting.getProtocolFromObd());
			ps.setString(5, devBasicSetting.getDevId());

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	
	public void updateDevBasicSettingStatus(String devId, String colName, int statusCode) {
		
		String sql = "update DEV_BASIC_SETTING set " + colName + "=?";
		if("ENGINE_PARAM_CONFIG_STATUS".equalsIgnoreCase(colName)) {
			sql += ", ENGINE_PARAM_CONFIG_DATE = SYSDATE ";
		}
		sql += " where DEV_ID = ?";
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)) {
			ps.setInt(1, statusCode);
			ps.setString(2, devId);
	
			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		} 
	}

	public void addToDevBasicSettingBatch(DevBasicSetting devBasicSetting) {
		DevBasicSetting latestDevBasicSetting = lastDBSs.get(devBasicSetting.getDevId());
		if (latestDevBasicSetting == null
				|| devBasicSetting.getLastUpdate().after(latestDevBasicSetting.getLastUpdate())) {
            lastDBSs.put(devBasicSetting.getDevId(), devBasicSetting);
        }
	}

	public void batchUpdateDevBasicSetting() {
		if (lastDBSs.size() > 0) {
			long start = System.currentTimeMillis();
			try (PreparedStatement ps = ConnectionGettor.prepareStatement(DeviceDao.SQL_DEV_BASIC_SETTING_UPDATE)) {
				List<DevBasicSetting> sortedList = ArrayUtils.getSortedList(new ArrayList<>(lastDBSs.values()), Comparator.comparing(DevBasicSetting::getDevId));
				for (DevBasicSetting devBasicSetting : sortedList) {
					setSqlDevBasicSettingUpdate(ps, devBasicSetting);
					ps.addBatch();
				}

				int[] numUpdates = ps.executeBatch();
				log.info("[BATCH] DBS Online updated: " + numUpdates.length + " - " + (System.currentTimeMillis() - start) + "ms");
			} catch (BatchUpdateException buex) {
				log.error("Got error during batch update for entities: {}", lastDBSs, buex);
				if (buex.getUpdateCounts() != null) {
					int[] updateCounts = buex.getUpdateCounts();
                    int totalCount = lastDBSs.size();
                    int successCount = updateCounts.length;
                    int failCount = lastDBSs.size() - successCount;

                    log.info("[RETRY] Total: " + totalCount + ", Success: " + successCount + ", Fail: " + failCount);
                    for (int i = successCount; i < lastDBSs.size(); i++) {
                        DevBasicSetting devBasicSetting = lastDBSs.values().toArray(new DevBasicSetting[0])[i];
                        updateDevBasicSetting(devBasicSetting);
                        log.info("[RETRY] Done...(" + (i + 1) + "/" + lastDBSs.size() + ")");
                    }
				}
			} catch (Exception e) {
				log.error("[ERROR] " + e.toString(), e);
			} finally {
				lastDBSs.clear();
			}
		}
	}

	private void updateDevBasicSetting(DevBasicSetting devBasicSetting) {
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(DeviceDao.SQL_DEV_BASIC_SETTING_UPDATE)) {
			setSqlDevBasicSettingUpdate(ps, devBasicSetting);
            ps.executeUpdate();
		} catch (Exception e) {
            log.error("[ERROR] " + e.toString(), e);
        }
	}

	private void setSqlDevBasicSettingUpdate(PreparedStatement ps, DevBasicSetting devBasicSetting) throws Exception {
		if (devBasicSetting.getTotalMileage() == null) {
			ps.setNull(1, java.sql.Types.BIGINT);
		} else {
			ps.setLong(1, devBasicSetting.getTotalMileage());
		}
		if (devBasicSetting.getTotalFuelConsu() == null) {
			ps.setNull(2, java.sql.Types.DOUBLE);
		} else {
			ps.setDouble(2, devBasicSetting.getTotalFuelConsu());
		}
		if (devBasicSetting.getGsmSignal() == null) {
			ps.setNull(3, java.sql.Types.INTEGER);
		} else {
			ps.setInt(3, devBasicSetting.getGsmSignal());
		}
		if (devBasicSetting.getOdometer() == null) {
			ps.setNull(4, Types.FLOAT);
		} else {
			ps.setFloat(4, devBasicSetting.getOdometer());
		}
		if (devBasicSetting.getTotalEngineHour() == null) {
			ps.setNull(5, Types.FLOAT);
		} else {
			ps.setFloat(5, devBasicSetting.getTotalEngineHour());
		}
		if (devBasicSetting.getBatteryVoltage() == null) {
			ps.setNull(6, Types.VARCHAR);
		} else {
			ps.setString(6, devBasicSetting.getBatteryVoltage());
		}
		ps.setString(7, devBasicSetting.getDevId());
	}
	
	// TODO make generic method for all columns.
	public void updateDevBasicSetting(float totalEngienHour, float odometer, int fuelLevel, int fuelLevel2, String divId,
									  float batteryVoltage,
									  float oilTemperature,
									  float engineRefTorque,
									  float fuelRailPressure,
									  Float remainingRange, Integer battery,
									  Integer isCharging) {
		String sqlStr = "update DEV_BASIC_SETTING set ";
		ArrayList<String> list = new ArrayList<>();
		
		DecimalFormat df = new DecimalFormat("#.####"); // '%.4f' in c++
//		log.debug(df.format(312.2));
		DecimalFormat df1Data = new DecimalFormat( "#.#" );
		if (totalEngienHour > 0) {
			list.add("total_engine_hour = " + df.format(totalEngienHour));
		}
		if (odometer > 0) {
			list.add("odometer = " + df.format(odometer));
		}
		
		if (fuelLevel >= 0) {
			list.add("fuel_level = " + fuelLevel);
		}
		if (fuelLevel2 >= 0) {
			list.add("fuel_level2 = " + fuelLevel2);
		}
		if(batteryVoltage > 0) {
			list.add("curr_battery_voltage = " + df1Data.format(batteryVoltage));
		}
		if(oilTemperature > 0) {
			list.add("curr_coolant_temp = " + df1Data.format(oilTemperature));
		}
		if(engineRefTorque > 0) {
			list.add("engine_ref_torque = " + df1Data.format(engineRefTorque));
		}
		if(fuelRailPressure > 0) {
			list.add("fuel_rail_pressure = " + df1Data.format(fuelRailPressure));
		}
		if(remainingRange != null && remainingRange >= 0) {
			list.add("remaining_range = " + df1Data.format(remainingRange));
		}
		if(battery !=null && battery >= 0) {
			list.add("battery = " + battery);
		}
		if(isCharging !=null) {
			list.add("is_charging = " + isCharging);
		}

		if (list.size() <= 0) {
			log.warn("[DeviceDao.updateDeviceStatus] Bad Params");
			return;
		}
		
		String setValues = String.join(", ", list);
		String query = sqlStr + setValues + " where dev_id = '" + divId + "'";
		
		log.debug("[DeviceDao.updateDeviceStatus] " + query);

		try (Statement statement = ConnectionGettor.createStatement()){
			statement.execute(query);
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}
	
//	public void updateDeviceStatus(int status, long totalTripMileage, float totalFuel, String divId) {
//		// "update DEV_BASIC_SETTING set ON_LINE = 0,TOTAL_MILEAGE=%u,TOTAL_FUEL_CONSU=%.2f where DEV_ID = '%s'"
//		String sqlStr = "update DEV_BASIC_SETTING set ON_LINE=%1$d,TOTAL_MILEAGE=%2$d,TOTAL_FUEL_CONSU=%3$.2f where DEV_ID='%4$s'";
//		String query = String.format(sqlStr, status, totalTripMileage, totalFuel, divId);
//		try (Statement statement = ConnectionGettor.createStatement()){
//			statement.execute(query);
//		} catch (SQLException e) {
//			log.error(e.getMessage());
//		}
//	}

//	public void updateDeviceStatus(int status, long totalTripMileage, float totalFuel, String divId, int gsmSignal) {
//		// "update DEV_BASIC_SETTING set ON_LINE = 0,TOTAL_MILEAGE=%u,TOTAL_FUEL_CONSU=%.2f where DEV_ID = '%s'"
//		String sqlStr = "update DEV_BASIC_SETTING set ON_LINE=%1$d,TOTAL_MILEAGE=%2$d,TOTAL_FUEL_CONSU=%3$.2f,GSM_SIGNAL=%4$d where DEV_ID='%5$s'";
//		String query = String.format(sqlStr, status, totalTripMileage, totalFuel, gsmSignal, divId);
//		try (Statement statement = ConnectionGettor.createStatement()){
//			statement.execute(query);
//		} catch (SQLException e) {
//			log.error(e.getMessage());
//		}
//	}
	
	public boolean isAlarmDisabledDevice(String devId) {
		String sqlStr = "select 1 from group_info g inner join device d on d.group_id = g.group_id and g.acct_id in (1502,1482,1542,1802,1803,3240) and d.dev_id=?";
		boolean exist = false;
		try (PreparedStatement statement = ConnectionGettor.prepareStatement(sqlStr)){
			statement.setString(1, devId);
			//Device device = jdbcTemplate.queryForObject(query, Device.class);
			ResultSet rs = statement.executeQuery();
			if (rs.next()) exist = true;
			rs.close();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
		return exist;
	}
	
	public Map<String, Long> getDeviceIdByAcctId(List<Long> acctIds) {
		String sqlStr = "SELECT d.dev_id, g.acct_id FROM group_info g INNER JOIN device d ON d.group_id = g.group_id AND g.acct_id IN (%s)";
		 String sqlIN = acctIds.stream()
					.map(String::valueOf)
					.collect(Collectors.joining(","));
		String query = String.format(sqlStr, sqlIN);
		Map<String, Long> deviceMap = null;
		try (Statement statement = ConnectionGettor.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_READ_ONLY)){
			ResultSet rs = statement.executeQuery(query);
			rs.beforeFirst();
			rs.last();
			int size = rs.getRow();
			if(size > 0) {
				deviceMap = new HashMap<>(size);
			}
			log.info("FETCH SIZE: " + size);
			rs.beforeFirst();
			while (rs.next()) {
				deviceMap.put(rs.getString(1), rs.getLong(2));
			}
			rs.close();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
		return deviceMap;
	}

	public void updateConnectionStat(String devId, String currentTime) {
		String sql = "update ADM_DEV_CONNECTION_STAT set LAST_EVENT_TIME = TO_TIMESTAMP(?,'yyyy-MM-dd hh24:mi:ss'), LAST_UPDATE_TIME=systimestamp " +
				"where DEV_ID = ? and LAST_EVENT_TIME < TO_TIMESTAMP(?,'yyyy-MM-dd hh24:mi:ss')";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, currentTime);
			ps.setString(2, devId);
			ps.setString(3, currentTime);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void saveConnectionStat(String devId, String currentTime) {
		String sql = "insert into ADM_DEV_CONNECTION_STAT (DEV_ID,LAST_EVENT_TIME,LAST_UPDATE_TIME) values (?,TO_TIMESTAMP(?,'yyyy-MM-dd hh24:mi:ss'), systimestamp)";
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devId);
			ps.setString(2, currentTime);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public String getLastEventTime(String devId) {
		String sql = "select TO_CHAR(LAST_EVENT_TIME, 'yyyy-mm-dd hh24:mi:ss') LAST_EVENT_TIME from ADM_DEV_CONNECTION_STAT where DEV_ID = ?";
		String lastEventTime = null;
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devId);

			ResultSet rs = ps.executeQuery();
			while (rs.next()) {
				lastEventTime = rs.getString("LAST_EVENT_TIME");
			}
			rs.close();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
		return lastEventTime;
	}

	public void updateWorkMode(String devId, Integer workMode) {
		String sql = "update dev_basic_setting set work_mode = ? where dev_id = ?";
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setInt(1, workMode);
			ps.setString(2, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			System.out.println(e.getMessage());
		}
	}

    public String getDevFWVersion(String devId) {
		String sql = "SELECT DEV_FW_VER FROM DEV_BASIC_SETTING WHERE DEV_ID = ?";
		String devFwVersion = null;
		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devId);

			ResultSet rs = ps.executeQuery();
			while (rs.next()) {
				devFwVersion = rs.getString("DEV_FW_VER");
			}
			rs.close();
		}catch (SQLException e) {
			log.error(e.getMessage());
		}
		return devFwVersion;
    }

	public void updateVinCode(String devId, String vinCode) {
		String sql = "update DEV_BASIC_SETTING set VEHICLE_VIN_CODE = ? where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, vinCode);
			ps.setString(2, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void updateFW(String devId, String fw) {
		String sql = "update DEV_BASIC_SETTING set DEV_FW_VER = ? where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, fw);
			ps.setString(2, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void updateSupportPids(String devId, String supportCods) {
		String sql = "update DEV_BASIC_SETTING set PIDS_SUPPORT_CODS = ? where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, supportCods);
			ps.setString(2, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void updateEngineCutStatus(String devId, Integer engineCutStatus, Integer engineCutConfigStatus) {
		String sql = "update DEV_BASIC_SETTING set ENGINE_CUT_CONFIG_DATE=sysdate ";

		if (engineCutStatus != null && engineCutStatus >= 0) {
			sql += ", ENGINE_CUT_STATUS=" + engineCutStatus;
		}

		if (engineCutConfigStatus != null && engineCutConfigStatus > 0) {
			sql += ", ENGINE_CUT_CONFIG_STATUS=" + engineCutConfigStatus;
		}

		sql += " where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void updateNetConfigStatus(String devId, Integer status) {
		String sql = "update ADM_DEV_NET_CONFIG set LAST_UPDATE_TIME=sysdate ";

		if (status != null && status > 0) {
			sql += ", STATUS=" + status;
		}

		sql += " where DEV_ID = ?";

		try (PreparedStatement ps = ConnectionGettor.prepareStatement(sql)){
			ps.setString(1, devId);

			ps.executeUpdate();
		} catch (SQLException e) {
			log.error(e.getMessage());
		}
	}

	public void saveDevParameters(String devId,String paramName,String paramValue) {
		if (paramValue != null && paramValue.trim().length() > 100){
			log.warn("too long paramValue : {} -> {}",devId,paramValue);
			return;
		}
		try (PreparedStatement preparedStatement = ConnectionGettor.prepareStatement(mergeDevParamSql())){
			preparedStatement.setObject(1,paramName);
			preparedStatement.setObject(2,devId);
			preparedStatement.setObject(3,paramValue);
			preparedStatement.setObject(4,devId);
			preparedStatement.setObject(5,paramName);
			preparedStatement.setObject(6,paramValue);
			preparedStatement.executeUpdate();
		} catch (SQLException e) {
			log.error("Process Devparameter error ",e);
		}
	}



	public EngineCutStatus getengineCutStatus(String devId){
		String sql = "SELECT ENGINE_CUT_STATUS , ENGINE_CUT_CONFIG_STATUS FROM DEV_BASIC_SETTING WHERE DEV_ID = ?";
		try (PreparedStatement preparedStatement = ConnectionGettor.prepareStatement(sql)){
			preparedStatement.setObject(1,devId);
			ResultSet resultSet = preparedStatement.executeQuery();
			if (resultSet.next()){
				return EngineCutStatus.builder().engineCutStatus(resultSet.getInt(1)).engineCutConfigStatus(resultSet.getInt(2)).devId(devId).build();
			}
		} catch (SQLException e) {
			log.error("Update engine cut status error ",e);
		}
		return null;
	}

	/**
	 *
	 * @param devId
	 * @param status        ENGINE_CUT_STATUS          Engine cut status. 0:recover, 1:cut
	 * @param configStatus  ENGINE_CUT_CONFIG_STATUS   Engine cut config status. 1:in progress, 2:successful,3:failed
	 */
	public void updateEngineCutStatus(String devId, int status,int configStatus){
		String sql = "UPDATE DEV_BASIC_SETTING SET ENGINE_CUT_STATUS = ?, ENGINE_CUT_CONFIG_STATUS = ?, ENGINE_CUT_CONFIG_DATE = SYSDATE WHERE DEV_ID = ?";
		try (PreparedStatement preparedStatement = ConnectionGettor.prepareStatement(sql)){
			preparedStatement.setObject(1,status);
			preparedStatement.setObject(2,configStatus);
			preparedStatement.setObject(3,devId);
			preparedStatement.executeUpdate();
		} catch (SQLException e) {
			log.error("Update engine cut status error ",e);
		}
	}


	private String mergeDevParamSql() {
		StringBuilder builder = new StringBuilder()
				.append(" merge into ADM_DEV_PARAMETER t1 ")
				.append(" using (select ? as param_name,? as dev_id from dual) t2 ")
				.append(" on (t1.param_name = t2.param_name and t1.dev_id = t2.dev_id )")
				.append(" when matched then ")
				.append(" update set PARAM_VALUE=?,OPERATION_TYPE= 0,LAST_UPDATE_TIME=systimestamp ")
				.append(" when not matched then ")
				.append(" insert values (?,?,?,0,systimestamp)");
		return builder.toString();
	}


}
