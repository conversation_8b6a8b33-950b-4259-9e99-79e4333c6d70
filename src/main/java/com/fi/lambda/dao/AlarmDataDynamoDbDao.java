package com.fi.lambda.dao;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper.FailedBatch;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperConfig;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperConfig.TableNameOverride;
import com.amazonaws.services.dynamodbv2.model.WriteRequest;
import com.amazonaws.util.CollectionUtils;
import com.fi.lambda.model.AlarmData;

import lombok.extern.log4j.Log4j2;

import java.util.*;

@Log4j2
public class AlarmDataDynamoDbDao {

	private DynamoDBMapper mapper;
    private ArrayList<AlarmData> dummyDeleteBatch = new ArrayList<>();

    private static final int WRITE_RETRY = 1;
    private DynamoDBMapperConfig.DefaultBatchWriteRetryStrategy batchWriteRetryStrategy = new DynamoDBMapperConfig.DefaultBatchWriteRetryStrategy(WRITE_RETRY);
    private String tablenamePrefix;

    private HashMap<Integer, Set<AlarmData>> batchMap;

    public AlarmDataDynamoDbDao(DynamoDBMapper mapper) {
        this.mapper = mapper;
        batchMap = new HashMap<>();
    }

    public void setTableNamePrefix(String envStage) {
        tablenamePrefix = envStage + "-alarmdata-"; // stress-gpsdata-201909
        batchMap.clear();
    }


    private Set<AlarmData> getWriteBatchList(long tmTime) {
        int yyyymm = (int) (tmTime / 100000000L);
        if (!batchMap.containsKey(yyyymm)) {
            batchMap.put(yyyymm, new HashSet<>());
        }
        return batchMap.get(yyyymm);
    }

    public void batchInsertWithRetry() {
        for (Map.Entry<Integer, Set<AlarmData>> entry : batchMap.entrySet()) {
            long start = System.currentTimeMillis();

            String tableName = tablenamePrefix + entry.getKey();
            DynamoDBMapperConfig config = getDDBMapperConfig(tableName);

            Set<AlarmData> writeBatch = entry.getValue();

            List<FailedBatch> failedBatchList = mapper.batchWrite(writeBatch, dummyDeleteBatch, config);
            logFailedBatch(failedBatchList);

            log.info("DynamoDB batchInsert alarm_data. Size: " + writeBatch.size() + ", ms: " + (System.currentTimeMillis() - start) + "ms");
        }
        batchMap.clear();
    }

    private DynamoDBMapperConfig getDDBMapperConfig(String tableName) {
        return DynamoDBMapperConfig.builder()
                .withBatchWriteRetryStrategy(batchWriteRetryStrategy)
                .withTableNameOverride(TableNameOverride.withTableNameReplacement(tableName))
                .build();
    }

    private void logFailedBatch(List<FailedBatch> failedBatchList) {
        if (CollectionUtils.isNullOrEmpty(failedBatchList)) {
            log.debug("failBatch is null or empty.");
            return;
        }

        log.info("DynamoDB alarm_data failedBatchSize: " + failedBatchList.size());
        for (FailedBatch failedBatch : failedBatchList) {
            String errorMsg = failedBatch.getException().getMessage();
            Map<String, List<WriteRequest>> items = failedBatch.getUnprocessedItems();
            for (Map.Entry<String, List<WriteRequest>> entity : items.entrySet()) {
                String tableName = entity.getKey();
                List<WriteRequest> writeRequestList = entity.getValue();
                for (WriteRequest wr : writeRequestList) {
                    String wrJson = wr.toString();
                    log.warn("[WARN] DynamoDB alarm_data FailedBatch, " + errorMsg + "," + tableName + "," + wrJson);
                }
            }
        }
    }

    public AlarmData addToBatchList(String devId, long acconTime, long deviceTime, int alarmType, int alarmDescription, long iLat, long iLon) {
        AlarmData alarmData = new AlarmData(devId, acconTime, deviceTime, alarmType, alarmDescription, iLat, iLon);
        Set<AlarmData> writeBatch = getWriteBatchList(deviceTime);
        writeBatch.add(alarmData);
        return alarmData;
    }
}

