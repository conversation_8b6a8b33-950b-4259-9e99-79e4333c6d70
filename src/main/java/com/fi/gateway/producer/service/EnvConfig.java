package com.fi.gateway.producer.service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang.StringUtils;

import com.fleetup.rds.Env;

import lombok.experimental.UtilityClass;

@UtilityClass
public class EnvConfig {
	
	private static boolean isThridPartyStream;
	
	public static void init() {
		isThridPartyStream = StringUtils.isNotEmpty(System.getenv("THIRD_PARTY_ACCOUNT_IDS"));
	}

	public static String getKinesisEndpoint() {
		return !"prod".equalsIgnoreCase(Env.getStage()) ? "stress-nonobd-sub-stream" : Env.getStage() + "-nonobd-sub-stream";
	}
	
	public static List<Long> getAccountIds() {
		String acctStr = System.getenv("THIRD_PARTY_ACCOUNT_IDS");
		return StringUtils.isEmpty(acctStr) ? null
				: Stream.of(acctStr.split(",")).map(x -> Long.parseLong(x.trim())).collect(Collectors.toList());
	}
	
	
	public static boolean isInitialized() {
		return isThridPartyStream;
	}
}
