/*
 * Copyright 2015 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Amazon Software License (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 * http://aws.amazon.com/asl/
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */

package com.fi.gateway.producer.service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import com.amazonaws.kinesis.agg.AggRecord;
import com.amazonaws.kinesis.agg.RecordAggregator;
import com.amazonaws.services.kinesis.AmazonKinesis;
import com.amazonaws.util.CollectionUtils;
import com.fleetup.rds.Env;

import lombok.extern.log4j.Log4j2;

/**
 * The Kinesis Producer Library (KPL) excels at handling large numbers of small
 * logical records by combining multiple logical records into a single Kinesis
 * record.
 * 
 * <p>
 * In this sample we'll be putting a monotonically increasing sequence number in
 * each logical record, and then padding the record to 128 bytes long. The
 * consumer will then check that all records are received correctly by verifying
 * that there are no gaps in the sequence numbers.
 * 
 * <p>
 * We will distribute the records evenly across all shards by using a random
 * explicit hash key.
 * 
 * <p>
 * To prevent the consumer from being confused by data from multiple runs of the
 * producer, each record also carries the time at which the producer started.
 * The consumer will reset its state whenever it detects a new, larger
 * timestamp. We will place the timestamp in the partition key. This does not
 * affect the random distribution of records across shards since we've set an
 * explicit hash key.
 * 
 * @see OCDKinesisProducerService
 *
 */
@Log4j2
public class KinesisProducerService {


	/**
	 * Timestamp we'll attach to every record
	 */
	private static final String TIMESTAMP = Long.toString(System.currentTimeMillis());

	/**
	 * Change these to try larger or smaller records.
	 */
	private static final int DATA_SIZE = 128;

	/**
	 * Put records for this number of seconds before exiting.
	 */
	private static final int SECONDS_TO_RUN = 5;

	/**
	 * Put this number of records per second.
	 * 
	 * Because multiple logical records are combined into each Kinesis record, even
	 * a single shard can handle several thousand records per second, even though
	 * there is a limit of 1000 Kinesis records per shard per second.
	 * 
	 * If a shard gets throttled, the KPL will continue to retry records until
	 * either they succeed or reach a TTL set in the KPL's configuration, at which
	 * point the KPL will return failures for those records.
	 * 
	 * @see {@link KinesisProducerConfiguration#setRecordTtl(long)}
	 */
	private static final int RECORDS_PER_SECOND = 2000;

	/**
	 * Change this to your stream name.
	 */
	private String streamName;

	/**
	 * Change this to the region you are using.
	 */
	private static final String REGION = Env.getRegion();
	
	private List<ByteBuffer> records;

	public KinesisProducerService(String streamName) {
		this.streamName = streamName;
		records = new ArrayList<>();
	}

	/**
	 * Here'll walk through some of the config options and create an instance of
	 * KinesisProducer, which will be used to put records.
	 * 
	 * @return KinesisProducer instance used to put records.
	 */
	public static AmazonKinesis getKinesisProducer() {
		return ProducerUtils.getKinesisProducer(REGION);
	}

	private static AmazonKinesis producer = null;

	// The monotonically increasing sequence number we will put in the data of each
	// record
	private final AtomicLong sequenceNumber = new AtomicLong(0);

	// The number of records that have finished (either successfully put, or failed)
	private final AtomicLong completed = new AtomicLong(0);

	public void addRecords(ByteBuffer byteBuffer) {
		records.add(byteBuffer);
	}

	public void publishRecord() {
		if (!CollectionUtils.isNullOrEmpty(records)) {
			log.info("Start forwarding {} deaggregated Kinesis records to {}", records.size(), streamName);
			if (producer == null) {
				producer = getKinesisProducer();
			}
			try {
				AggRecord aggRecord = null;
				RecordAggregator aggregator = new RecordAggregator();
				for (ByteBuffer deviceEntry : records) {
					aggRecord = aggregator.addUserRecord(ProducerUtils.randomExplicitHashKey(), deviceEntry.array());
					if (aggRecord != null) {
						sendRecord(producer, streamName, aggRecord);
					}
				}
				aggRecord = aggregator.clearAndGet();
				if (aggRecord != null) {
					sendRecord(producer, streamName, aggRecord);
				}

			} catch (Exception e) {
				log.error("KinesisForwarder encountered fatal error.", e);
			} finally {
				records.clear();
			}
		}
	}

	private static void sendRecord(AmazonKinesis producer, String streamName, AggRecord aggRecord) {
		if (aggRecord == null || aggRecord.getNumUserRecords() == 0) {
			return;
		}

		log.debug("Forwarding an aggregated record [NumRecords="
				+ aggRecord.getNumUserRecords() + " NumBytes=" + aggRecord.getSizeBytes() + "].");
		try {
			producer.putRecord(aggRecord.toPutRecordRequest(streamName));
		} catch (Exception e) {
			log.error("Failed to forward Kinesis records to destination stream.", e);
		}
		log.debug("Completed forwarding the aggregated record EHK=" + aggRecord.getExplicitHashKey());
	}
	
	public void destroy() {
		if(producer!=null) {
			producer.shutdown();
		}
		producer = null;
	}
	
}
