package com.fi.gateway.producer.event;

public class LoginEvent {

	private Stat stat;
	private Gps gps;
	private String softwareVer;
	private String hardwareVer;
	private int[] newParams;

	public Stat getStat() {
		return stat;
	}

	public void setStat(Stat stat) {
		this.stat = stat;
	}

	public Gps getGps() {
		return gps;
	}

	public void setGps(Gps gps) {
		this.gps = gps;
	}

	public String getSoftwareVer() {
		return softwareVer;
	}

	public void setSoftwareVer(String softwareVer) {
		this.softwareVer = softwareVer;
	}

	public String getHardwareVer() {
		return hardwareVer;
	}

	public void setHardwareVer(String hardwareVer) {
		this.hardwareVer = hardwareVer;
	}

	public int[] getNewParams() {
		return newParams;
	}

	public void setNewParams(int[] newParams) {
		this.newParams = newParams;
	}
}
