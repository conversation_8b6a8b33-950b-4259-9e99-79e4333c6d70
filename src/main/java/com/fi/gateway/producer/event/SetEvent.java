package com.fi.gateway.producer.event;

public class SetEvent {
	private int cmdSeq;
	private int successCount;
	private int[] tag;
	
	public SetEvent() {
	}
	public SetEvent(int cmdSeq, int successCount, int[] tag) {
		this.cmdSeq = cmdSeq;
		this.tag = tag;
		this.successCount = successCount;
	}
	public int getCmdSeq() {
		return cmdSeq;
	}
	public void setCmdSeq(int cmdSeq) {
		this.cmdSeq = cmdSeq;
	}
	public int getSuccessCount() {
		return successCount;
	}
	public void setSuccessCount(int successCount) {
		this.successCount = successCount;
	}
	public int[] getTag() {
		return tag;
	}
	public void setTag(int[] tag) {
		this.tag = tag;
	}
	
}
