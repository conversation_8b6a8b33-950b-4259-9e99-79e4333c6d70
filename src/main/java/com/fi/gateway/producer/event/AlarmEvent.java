package com.fi.gateway.producer.event;

public class AlarmEvent {

	//private long alarmSeq;
	private Stat stat;
	private Gps gps;
	private Alarm[] alarm;
	
//	public long getAlarmSeq() {
//		return alarmSeq;
//	}
//	public void setAlarmSeq(long alarmSeq) {
//		this.alarmSeq = alarmSeq;
//	}
	public Stat getStat() {
		return stat;
	}
	public void setStat(Stat stat) {
		this.stat = stat;
	}
	public Gps getGps() {
		return gps;
	}
	public void setGps(Gps gps) {
		this.gps = gps;
	}
	public Alarm[] getAlarm() {
		return alarm;
	}
	public void setAlarm(Alarm[] alarm) {
		this.alarm = alarm;
	}
}
