package com.fi.gateway.producer.event;

/**
 * <AUTHOR>
 * @date 2021/3/25
 * @desc
 */
public class UpgradeProgressEvent {
    private Integer receiveMark;

    private Integer messageIndex;

    public Integer getReceiveMark() {
        return receiveMark;
    }

    public void setReceiveMark(Integer receiveMark) {
        this.receiveMark = receiveMark;
    }

    public Integer getMessageIndex() {
        return messageIndex;
    }

    public void setMessageIndex(Integer messageIndex) {
        this.messageIndex = messageIndex;
    }
}
