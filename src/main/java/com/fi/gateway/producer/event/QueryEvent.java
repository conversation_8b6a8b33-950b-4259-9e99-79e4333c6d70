package com.fi.gateway.producer.event;

public class QueryEvent {
	private int cmdSeq;
	private int tag;
	private boolean isSuccess;
	private String value;
	
	public QueryEvent() {
	}
	
	public QueryEvent(int cmdSeq, int tag, boolean isSuccess, String value) {
		this.cmdSeq = cmdSeq;
		this.tag = tag;
		this.isSuccess = isSuccess;
		this.value = value;
	}
	
	public int getCmdSeq() {
		return cmdSeq;
	}
	public void setCmdSeq(int cmdSeq) {
		this.cmdSeq = cmdSeq;
	}
	public int getTag() {
		return tag;
	}
	public void setTag(int tag) {
		this.tag = tag;
	}
	public boolean isSuccess() {
		return isSuccess;
	}
	public void setSuccess(boolean isSuccess) {
		this.isSuccess = isSuccess;
	}
	public Object getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
}
