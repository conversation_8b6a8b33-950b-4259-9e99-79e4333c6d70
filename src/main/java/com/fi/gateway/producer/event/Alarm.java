package com.fi.gateway.producer.event;

/**
 * Alarm
 * 
 * <AUTHOR> SZ Team
 *
 */
public class Alarm {
	private int newAlarmFlag;
	private int alarmType;
	private int alarmDescription;
	//private int alarmThreshold; // not used.
	
	// 1: alarm triggered, new alarm  
	// 0: alarm eliminated, alarm end
	public boolean isNewAlarm() {
		return newAlarmFlag == 1;
	}
	public int getAlarmType() {
		return alarmType;
	}
	public void setAlarmType(int alarmType) {
		this.alarmType = alarmType;
	}
	public int getAlarmDescription() {
		return alarmDescription;
	}
	public void setAlarmDescription(int alarmDescription) {
		this.alarmDescription = alarmDescription;
	}
	/*
	public int getAlarmThreshold() {
		return alarmThreshold;
	}
	public void setAlarmThreshold(int alarmThreshold) {
		this.alarmThreshold = alarmThreshold;
	}
	*/
	private int getNewAlarmFlag() {
		return newAlarmFlag;
	}
	private void setNewAlarmFlag(int newAlarmFlag) {
		this.newAlarmFlag = newAlarmFlag;
	}
	
}
