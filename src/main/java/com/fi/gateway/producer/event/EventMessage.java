package com.fi.gateway.producer.event;

import com.google.gson.annotations.SerializedName;

public class EventMessage<T> {

	@SerializedName("devId")
	String devId;
	@SerializedName("protocolId")
	int protocolId;
	@SerializedName("data")
	T data;
	/**
	 * added a field to enhance device connection status
	 * to avoid engine cut/recover disconnection issue.
	 */
	String currentTime;
	
	public EventMessage() {
	}
	
	public EventMessage(int protocolId, String devId, T data) {
		this.protocolId = protocolId;
		this.devId = devId;
		this.data = data;
	}
	
	public String getDevId() {
		return devId;
	}
	public void setDevId(String devId) {
		this.devId = devId;
	}
	public int getProtocolId() {
		return protocolId;
	}
	public void setProtocolId(int protocolId) {
		this.protocolId = protocolId;
	}
	public T getData() {
		return data;
	}
	public void setData(T data) {
		this.data = data;
	}

	public String getCurrentTime() {
		return currentTime;
	}

	public void setCurrentTime(String currentTime) {
		this.currentTime = currentTime;
	}
}
