package com.fi.gateway.producer.event;


/**
 * <AUTHOR>
 */
public class FotaHttpFtpResultEvent {
    public static final int UPGRADE_RESULT_SUCCESS = 0x00;
    public static final int UPGRADE_RESULT_FAIL = 0x01;
    public static final int UPGRADE_RESULT_CANCEL = 0x02;

    private int updateType;
    private int confirmation;

    public FotaHttpFtpResultEvent() {
    }

    public FotaHttpFtpResultEvent(int updateType, int confirmation) {
        this.updateType = updateType;
        this.confirmation = confirmation;
    }

    public int getUpdateType() {
        return updateType;
    }

    public void setUpdateType(int updateType) {
        this.updateType = updateType;
    }

    public int getConfirmation() {
        return confirmation;
    }

    public void setConfirmation(int confirmation) {
        this.confirmation = confirmation;
    }

}

