package com.fi.gateway.producer.event;

import java.util.List;

public class DtcEvent {
    private long latitude;
    private long longitude;
    private List<DtcDetail> dtcs;
    private long tmTime;

    public long getLatitude() {
        return latitude;
    }

    public void setLatitude(long latitude) {
        this.latitude = latitude;
    }

    public long getLongitude() {
        return longitude;
    }

    public void setLongitude(long longitude) {
        this.longitude = longitude;
    }

    public long getTmTime() {
        return tmTime;
    }

    public void setTmTime(long tmTime) {
        this.tmTime = tmTime;
    }

    public List<DtcDetail> getDtcs() {
        return dtcs;
    }

    public void setDtcs(List<DtcDetail> dtcs) {
        this.dtcs = dtcs;
    }

    public static class DtcDetail{
        private int code;
        private int carType;
        private String codeStr;
        private int flag;

        public DtcDetail(){}

        public DtcDetail(int code, int carType,String codeStr,int flag){
            this.code = code;
            this.carType = carType;
            this.codeStr = codeStr;
            this.flag = flag;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public int getCarType() {
            return carType;
        }

        public void setCarType(int carType) {
            this.carType = carType;
        }

        public String getCodeStr() {
            return codeStr;
        }

        public void setCodeStr(String codeStr) {
            this.codeStr = codeStr;
        }

        public int getFlag() {
            return flag;
        }

        public void setFlag(int flag) {
            this.flag = flag;
        }
    }
}
