package com.fi.gateway.producer.event;

/**
 * STAT_DATA
 * 
 * <AUTHOR> SZ Team
 *
 */
public class Gps {
	/**
	 * @deprecated
	 */
	int date;
	/**
	 * @deprecated
	 */
	int time;
	long latitude;
	long longitude;
	int speed;
	int direction;
	int locationStatus;
	int satellitesNumber;
	long tmTime;
	long devTime;
	
	/**
	 * @deprecated
	 */
	public int getDate() {
		return date;
	}
	public void setDate(int date) {
		this.date = date;
	}
	/**
	 * @deprecated
	 */
	public int getTime() {
		return time;
	}
	public void setTime(int time) {
		this.time = time;
	}
	public long getLatitude() {
		return latitude;
	}
	public void setLatitude(long latitude) {
		this.latitude = latitude;
	}
	public long getLongitude() {
		return longitude;
	}
	public void setLongitude(long longitude) {
		this.longitude = longitude;
	}
	public int getSpeed() {
		return speed;
	}
	public void setSpeed(int speed) {
		this.speed = speed;
	}
	public int getDirection() {
		return direction;
	}
	public void setDirection(int direction) {
		this.direction = direction;
	}
	public int getLocationStatus() {
		return locationStatus;
	}
	public void setLocationStatus(int locationStatus) {
		this.locationStatus = locationStatus;
	}
	public int getSatellitesNumber() {
		return satellitesNumber;
	}
	public void setSatellitesNumber(int satellitesNumber) {
		this.satellitesNumber = satellitesNumber;
	}
	
	/**
	 * @deprecated see get
	 * @return
	 */
	public long getGpsTime(){
		return 20000000000000L + date * 1000000L + time;
	}
	public long getTmTime() {
		return tmTime;
	}
	public void setTmTime(long tmTime) {
		this.tmTime = tmTime;
	}
	public long getDevTime() {
		return devTime;
	}
	public void setDevTime(long devTime) {
		this.devTime = devTime;
	}
}
