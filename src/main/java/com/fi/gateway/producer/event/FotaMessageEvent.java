package com.fi.gateway.producer.event;

public class FotaMessageEvent {
	private long updateId;
	private int messageIndex;
	private int receiveMark;
	
	public FotaMessageEvent() {
	}
	
	public FotaMessageEvent(long updateId, int messageIndex, int receiveMark) {
		this.updateId = updateId;
		this.messageIndex = messageIndex;
		this.receiveMark = receiveMark;
	}

	public long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(long updateId) {
		this.updateId = updateId;
	}

	public int getMessageIndex() {
		return messageIndex;
	}

	public void setMessageIndex(int messageIndex) {
		this.messageIndex = messageIndex;
	}

	public int getReceiveMark() {
		return receiveMark;
	}

	public void setReceiveMark(int receiveMark) {
		this.receiveMark = receiveMark;
	}

}
