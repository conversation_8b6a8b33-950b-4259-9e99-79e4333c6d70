package com.fi.gateway.producer.event;

/**
 * <AUTHOR>
 * @date 2022-12-13 11:16:12
 */
public class TlvEvent {
    private Integer cmdSeq;

    private Integer status;

    private Integer protocolId;

    private Integer tlvTag;

    private String tlvValue;

    public Integer getCmdSeq() {
        return cmdSeq;
    }

    public void setCmdSeq(Integer cmdSeq) {
        this.cmdSeq = cmdSeq;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(Integer protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getTlvTag() {
        return tlvTag;
    }

    public void setTlvTag(Integer tlvTag) {
        this.tlvTag = tlvTag;
    }

    public String getTlvValue() {
        return tlvValue;
    }

    public void setTlvValue(String tlvValue) {
        this.tlvValue = tlvValue;
    }

    @Override
    public String toString() {
        return "TlvEvent{" +
                "cmdSeq=" + cmdSeq +
                ", status=" + status +
                ", protocolId=" + protocolId +
                ", tlvTag=" + tlvTag +
                ", tlvValue='" + tlvValue + '\'' +
                '}';
    }
}
