package com.fi.gateway.producer.event;

/**
 * <AUTHOR>
 * @date 2021/3/25
 * @desc
 */
public class UpgradeNotifyEvent {
    private String firmwareVersion;

    private Integer updateConfirmation;

    public String getFirmwareVersion() {
        return firmwareVersion;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        this.firmwareVersion = firmwareVersion;
    }

    public Integer getUpdateConfirmation() {
        return updateConfirmation;
    }

    public void setUpdateConfirmation(Integer updateConfirmation) {
        this.updateConfirmation = updateConfirmation;
    }
}
