package com.fi.gateway.producer.event;

/**
 * STAT_DATA
 * 
 * <AUTHOR> SZ Team
 *
 */
public class Stat {
	private long acconTime;
	private long deviceTime;
	private long totalTripMileage;
	private long currentTripMileage;
	private String odometer;
	private String engineHour;
	private int batteryVoltage;
	private long totalFuel;
	private int currentFuel;
	private int engineDiagnoseProtocol;
	private long fuelWear;
	private int[] reserve;
	private int[] vState;

	public String getEngineHour() {
		return engineHour;
	}

	public void setEngineHour(String engineHour) {
		this.engineHour = engineHour;
	}

	public int getBatteryVoltage() {
		return batteryVoltage;
	}

	public void setBatteryVoltage(int batteryVoltage) {
		this.batteryVoltage = batteryVoltage;
	}

	public String getOdometer() {
		return odometer;
	}

	public void setOdometer(String odometer) {
		this.odometer = odometer;
	}

	public long getAcconTime() {
		return acconTime;
	}
	public void setAcconTime(long acconTime) {
		this.acconTime = acconTime;
	}
	public long getDeviceTime() {
		return deviceTime;
	}
	public void setDeviceTime(long deviceTime) {
		this.deviceTime = deviceTime;
	}
	public long getTotalTripMileage() {
		return totalTripMileage;
	}
	public void setTotalTripMileage(long totalTripMileage) {
		this.totalTripMileage = totalTripMileage;
	}
	public long getCurrentTripMileage() {
		return currentTripMileage;
	}
	public void setCurrentTripMileage(long currentTripMileage) {
		this.currentTripMileage = currentTripMileage;
	}
	public long getTotalFuel() {
		return totalFuel;
	}
	public void setTotalFuel(long totalFuel) {
		this.totalFuel = totalFuel;
	}
	public int getCurrentFuel() {
		return currentFuel;
	}
	public void setCurrentFuel(int currentFuel) {
		this.currentFuel = currentFuel;
	}
	public int getEngineDiagnoseProtocol() {
		return engineDiagnoseProtocol;
	}
	public void setEngineDiagnoseProtocol(int engineDiagnoseProtocol) {
		this.engineDiagnoseProtocol = engineDiagnoseProtocol;
	}

	public long getFuelWear() {
		return fuelWear;
	}

	public void setFuelWear(long fuelWear) {
		this.fuelWear = fuelWear;
	}

	public int[] getReserve() {
		return reserve;
	}

	public void setReserve(int[] reserve) {
		this.reserve = reserve;
	}

	public int[] getvState() {
		return vState;
	}

	public void setvState(int[] vState) {
		this.vState = vState;
	}
}
