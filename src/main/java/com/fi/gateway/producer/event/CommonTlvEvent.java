package com.fi.gateway.producer.event;

public class CommonTlvEvent {
    private int cmdSeq;

    private int[] tlvTag;

    private String[] tlvValues;

    public int getCmdSeq() {
        return cmdSeq;
    }

    public void setCmdSeq(int cmdSeq) {
        this.cmdSeq = cmdSeq;
    }

    public int[] getTlvTag() {
        return tlvTag;
    }

    public void setTlvTag(int[] tlvTag) {
        this.tlvTag = tlvTag;
    }

    public String[] getTlvValues() {
        return tlvValues;
    }

    public void setTlvValues(String[] tlvValues) {
        this.tlvValues = tlvValues;
    }
}
