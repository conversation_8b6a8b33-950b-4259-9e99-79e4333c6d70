package com.fi.gateway.producer.event;

/**
 * 0x4001
 * 
 * <AUTHOR> SZ Team
 *
 */
public class GpsEvent {

	long tripNumber;
	int historyStatus;
	int accStatus;
	Stat stat;
	Gps[] gps;
	int[] rpm;

	public int getAccStatus() {
		return accStatus;
	}

	public void setAccStatus(int accStatus) {
		this.accStatus = accStatus;
	}

	public long getTripNumber() {
		return tripNumber;
	}

	public void setTripNumber(long tripNumber) {
		this.tripNumber = tripNumber;
	}

	public int getHistoryStatus() {
		return historyStatus;
	}
	public void setHistoryStatus(int historyStatus) {
		this.historyStatus = historyStatus;
	}
	public Stat getStat() {
		return stat;
	}
	public void setStat(Stat stat) {
		this.stat = stat;
	}
	public Gps[] getGps() {
		return gps;
	}
	public void setGps(Gps[] gps) {
		this.gps = gps;
	}
	public int[] getRpm() {
		return rpm;
	}
	public void setRpm(int[] rpm) {
		this.rpm = rpm;
	}
	
}
