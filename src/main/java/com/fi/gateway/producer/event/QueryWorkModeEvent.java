package com.fi.gateway.producer.event;


/**
 * <AUTHOR>
 */
public class QueryWorkModeEvent {
    public QueryWorkModeEvent() {
    }

    private Integer workMode;

    public QueryWorkModeEvent(Integer workMode) {
        this.workMode = workMode;
    }

    public Integer getWorkMode() {
        return workMode;
    }

    public void setWorkMode(Integer workMode) {
        this.workMode = workMode;
    }
}

