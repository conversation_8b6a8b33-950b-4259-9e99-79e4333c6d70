package com.fi.gateway.producer.event;


/**
 * <AUTHOR>
 */
public class FotaHttpFtpNotificationEvent {
    public static final int UPDATE_CONFIRMATION_SUCCESS = 0x01;
    public static final int UPDATE_CONFIRMATION_FAIL = 0x00;

    public static final int TERMINAL_UPGRADE = 0;

    private int updateType;
    private int confirmation;

    public FotaHttpFtpNotificationEvent() {
    }

    public FotaHttpFtpNotificationEvent(int updateType, int confirmation) {
        this.updateType = updateType;
        this.confirmation = confirmation;
    }

    public int getUpdateType() {
        return updateType;
    }

    public void setUpdateType(int updateType) {
        this.updateType = updateType;
    }

    public int getConfirmation() {
        return confirmation;
    }

    public void setConfirmation(int confirmation) {
        this.confirmation = confirmation;
    }
}

