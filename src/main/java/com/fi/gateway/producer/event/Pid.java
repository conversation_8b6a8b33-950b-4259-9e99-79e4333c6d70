package com.fi.gateway.producer.event;

/**
 * STAT_DATA
 * 
 */
public class Pid {
	private int pidType;
	private float pidData;
	private int pidCat;
	private long acconTime;
	private long tmTime;
	private String pidDataStr;

	public int getPidType() {
		return pidType;
	}

	public void setPidType(int pidType) {
		this.pidType = pidType;
	}

	public float getPidData() {
		return pidData;
	}

	public void setPidData(float pidData) {
		this.pidData = pidData;
	}

	public int getPidCat() {
		return pidCat;
	}

	public void setPidCat(int pidCat) {
		this.pidCat = pidCat;
	}

	public long getAcconTime() {
		return acconTime;
	}

	public void setAcconTime(long acconTime) {
		this.acconTime = acconTime;
	}

	public long getTmTime() {
		return tmTime;
	}

	public void setTmTime(long tmTime) {
		this.tmTime = tmTime;
	}

	public String getPidDataStr() {
		return pidDataStr;
	}

	public void setPidDataStr(String pidDataStr) {
		this.pidDataStr = pidDataStr;
	}
	

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (acconTime ^ (acconTime >>> 32));
		result = prime * result + pidCat;
		result = prime * result + Float.floatToIntBits(pidData);
		result = prime * result + ((pidDataStr == null) ? 0 : pidDataStr.hashCode());
		result = prime * result + pidType;
		result = prime * result + (int) (tmTime ^ (tmTime >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Pid other = (Pid) obj;
		if (acconTime != other.acconTime)
			return false;
		if (pidCat != other.pidCat)
			return false;
		if (Float.floatToIntBits(pidData) != Float.floatToIntBits(other.pidData))
			return false;
		if (pidDataStr == null) {
			if (other.pidDataStr != null)
				return false;
		} else if (!pidDataStr.equals(other.pidDataStr))
			return false;
		if (pidType != other.pidType)
			return false;
		if (tmTime != other.tmTime)
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "Pid [pidType=" + pidType + ", pidData=" + pidData + ", pidCat=" + pidCat + ", acconTime=" + acconTime
				+ ", tmTime=" + tmTime + ", pidDataStr=" + pidDataStr + "]";
	}

}
