package com.fi.gateway.producer.event;


/**
 * <AUTHOR>
 */
public class FotaHttpFtpDownloadProgressEvent {
    public static final int UPDATE_CONFIRMATION_SUCCESS = 0x01;
    public static final int UPDATE_CONFIRMATION_FAIL = 0x00;

    private long upgradeType;
    private int downloadProgress;

    public FotaHttpFtpDownloadProgressEvent() {
    }

    public FotaHttpFtpDownloadProgressEvent(long upgradeType, int downloadProgress) {
        this.upgradeType = upgradeType;
        this.downloadProgress = downloadProgress;
    }

    public long getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(long upgradeType) {
        this.upgradeType = upgradeType;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }
}

