package com.fi.gateway.producer.event;

/**
 * <AUTHOR>
 * @date 2022-12-13 13:54:08
 */
public class TCPUpgradeNotifyEvent {
    private Integer seq;

    private Integer status;

    private Integer protocolType;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(Integer protocolType) {
        this.protocolType = protocolType;
    }

    @Override
    public String toString() {
        return "TCPUpgradeNotifyEvent{" +
                "seq=" + seq +
                ", status=" + status +
                ", protocolType=" + protocolType +
                '}';
    }
}
