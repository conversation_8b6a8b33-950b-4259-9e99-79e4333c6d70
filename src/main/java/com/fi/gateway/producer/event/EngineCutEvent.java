package com.fi.gateway.producer.event;

/**
 * <AUTHOR>
 * @date 2022-12-12 14:38:41
 */
public class EngineCutEvent {
    private Integer engineCutConfigStatus;

    private Integer engineCutStatus;

    public Integer getEngineCutConfigStatus() {
        return engineCutConfigStatus;
    }

    public void setEngineCutConfigStatus(Integer engineCutConfigStatus) {
        this.engineCutConfigStatus = engineCutConfigStatus;
    }

    public Integer getEngineCutStatus() {
        return engineCutStatus;
    }

    public void setEngineCutStatus(Integer engineCutStatus) {
        this.engineCutStatus = engineCutStatus;
    }

    @Override
    public String toString() {
        return "EngineCutEvent{" +
                "engineCutConfigStatus=" + engineCutConfigStatus +
                ", engineCutStatus=" + engineCutStatus +
                '}';
    }
}
