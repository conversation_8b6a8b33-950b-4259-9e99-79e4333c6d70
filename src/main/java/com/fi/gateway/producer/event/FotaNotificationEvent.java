package com.fi.gateway.producer.event;

public class FotaNotificationEvent {
	private long updateId;
	private String firmwareVersion;
	private int confirmation;
	
	public FotaNotificationEvent() {
	}
	
	public FotaNotificationEvent(long updateId, String firmwareVersion, int confirmation) {
		this.updateId = updateId;
		this.firmwareVersion = firmwareVersion;
		this.confirmation = confirmation;
	}

	public long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(long updateId) {
		this.updateId = updateId;
	}

	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}

	public int getConfirmation() {
		return confirmation;
	}

	public void setConfirmation(int confirmation) {
		this.confirmation = confirmation;
	}
	
}
