<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.fi.lambda</groupId>
	<artifactId>gateway-vl502-consumer</artifactId>
	<version>1.0.0</version>

	<properties>
		<jdk.version>17</jdk.version>
		<lombok.version>1.18.22</lombok.version>
		<log4j.version>2.17.1</log4j.version>
	</properties>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/io.lettuce/lettuce-core -->
		<dependency>
			<groupId>io.lettuce</groupId>
			<artifactId>lettuce-core</artifactId>
			<version>6.1.6.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- Lettuce Connection Pooling -->
		<!-- <dependency> -->
		<!-- <groupId>org.apache.commons</groupId> -->
		<!-- <artifactId>commons-pool2</artifactId> -->
		<!-- <version>2.4.3</version> -->
		<!-- </dependency> -->

		<!-- https://mvnrepository.com/artifact/junit/junit -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.hamcrest/hamcrest-all -->
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>3.12.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.github.stefanbirkner</groupId>
			<artifactId>system-rules</artifactId>
			<version>1.19.0</version>
			<scope>test</scope>
		</dependency>

		<!-- Minimal set of interface definitions for Java support in AWS Lambda -->

		<!-- Logging -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-lambda-java-log4j2</artifactId>
			<version>1.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j18-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<!-- The AWS Java SDK for AWS Lambda module holds the client classes that
			are used for communicating with AWS Lambda Service https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-lambda -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-lambda</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>jmespath-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.amazonaws/amazon-kinesis-deaggregator -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>amazon-kinesis-aggregator</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>amazon-kinesis-deaggregator</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>amazon-kinesis-client</artifactId>
			<version>1.9.2</version>
			<exclusions>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-dynamodb</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>jmespath-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-lambda-java-events</artifactId>
			<version>2.2.7</version>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-elasticache</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>aws-java-sdk-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.amazonaws</groupId>
					<artifactId>jmespath-java</artifactId>
				</exclusion>
			</exclusions>
			<scope>test</scope>
		</dependency>

		<!-- FleetUp Internal SDK -->
		<dependency>
			<groupId>com.fleetup.rds</groupId>
			<artifactId>primitive-jdbc</artifactId>
			<version>3.0.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.fleetup</groupId>
			<artifactId>common-aws-sdk</artifactId>
			<version>2.0.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.fleetup.common</groupId>
			<artifactId>fleetup-dtc-message</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.9.0</version>
        </dependency>
    </dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-bom</artifactId>
				<version>1.12.514</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<extensions>
			<extension>
				<groupId>com.gkatzioura.maven.cloud</groupId>
				<artifactId>s3-storage-wagon</artifactId>
				<version>2.3</version>
			</extension>
		</extensions>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>**/version.txt</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<excludes>
					<exclude>**/version.txt</exclude>
				</excludes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/classes/lib</outputDirectory>
							<includeScope>runtime</includeScope>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<source>${jdk.version}</source>
					<target>${jdk.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptorRefs>
						<descriptorRef>jar-with-dependencies</descriptorRef>
					</descriptorRefs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<includes>
									<include>${project.groupId}:${project.artifactId}</include>
								</includes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<!-- This section is related to uploading to S3, e.g., “mvn deploy”. -->
	<distributionManagement>
		<repository>
			<id>aws-release</id>
			<name>AWS Release Repository</name>
			<url>s3://fleetup.maven.trunk/release</url>
		</repository>
		<snapshotRepository>
			<id>aws-snapshot</id>
			<name>AWS Snapshot Repository</name>
			<url>s3://fleetup.maven.trunk/snapshot</url>
		</snapshotRepository>
	</distributionManagement>

	<!-- This section is related to downloading from S3, e.g., “mvn install”. -->
	<repositories>
		<repository>
			<id>aws-release</id>
			<name>AWS Release Repository</name>
			<url>s3://fleetup.maven.trunk/release</url>
		</repository>
		<repository>
			<id>aws-snapshot</id>
			<name>AWS Snapshot Repository</name>
			<url>s3://fleetup.maven.trunk/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>